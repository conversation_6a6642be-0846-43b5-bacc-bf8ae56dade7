// dataLoader.js - 数据加载模块

// 加载示例数据
async function loadSampleData() {
    try {
        showToast('正在加载示例数据...', 'info');

        // 从data文件夹加载三个文件
        const [response1, response2, response3] = await Promise.all([
            fetch('./data/report-000.json'),
            fetch('./data/report-001.json'),
            fetch('./data/report-002.json')
        ]);

        if (!response1.ok || !response2.ok || !response3.ok) {
            // 如果文件不存在，使用测试数据
            console.log('使用测试数据');
            window.leftData = getTestData1();
            window.rightData = getTestData2();
            window.targetData = getTestData3();
        } else {
            // 成功加载所有三个文件
            window.leftData = await response1.json();
            window.rightData = await response2.json();
            window.targetData = await response3.json();
            
            console.log('成功加载示例数据：');
            console.log('版本1 (report-000.json):', Object.keys(window.leftData).length, '个模块');
            console.log('版本2 (report-001.json):', Object.keys(window.rightData).length, '个模块');
            console.log('版本3 (report-002.json):', Object.keys(window.targetData).length, '个模块');
        }

        // 清空之前的更改状态
        storage.clearChangeStatus();
        storage.clearRevisions();

        storage.set('dataVersion', storage.get('dataVersion') + 1);

        // 保存版本
        storage.addVersion({
            left: window.leftData,
            right: window.rightData,
            target: window.targetData
        });

        // 记录操作历史
        storage.addHistory({
            type: 'data_load',
            detail: '加载示例数据 (report-000.json, report-001.json, report-002.json)'
        });

        // 更新版本标签显示
        document.querySelector('[data-version="revision"]').classList.add('has-data');

        compareData();
        showToast('示例数据加载成功。三个版本的数据已就绪！', 'success');
        
        // 如果已有版本3数据，提示用户可以使用修订模式
        if (window.targetData) {
            setTimeout(() => {
                showToast('提示：您可以先在对比模式下接受需要的更改，然后切换到修订模式查看修订建议。', 'info');
            }, 2000);
        }
    } catch (error) {
        console.error('加载示例数据失败:', error);
        showToast('加载数据失败: ' + error.message, 'error');
    }
}

// 文件上传
function uploadFiles() {
    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = true;
    input.accept = '.json';
    input.onchange = handleFileUpload;
    input.click();
}

// 处理文件上传
function handleFileUpload(event) {
    const files = Array.from(event.target.files);
    if (files.length < 2 || files.length > 3) {
        showToast('请选择2个或3个JSON文件进行对比', 'warning');
        return;
    }

    const readers = files.map(file => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result);
                    resolve(data);
                } catch (error) {
                    reject(error);
                }
            };
            reader.onerror = reject;
            reader.readAsText(file);
        });
    });

    Promise.all(readers)
        .then((results) => {
            window.leftData = results[0];
            window.rightData = results[1];
            window.targetData = results[2] || null;

            // 数据更改时，清空修订状态
            storage.clearRevisions();
            storage.clearChangeStatus();
            storage.set('dataVersion', storage.get('dataVersion') + 1);

            // 保存版本
            storage.addVersion({
                left: window.leftData,
                right: window.rightData,
                target: window.targetData
            });

            // 记录操作历史
            storage.addHistory({
                type: 'file_upload',
                detail: `上传${files.length}个文件`
            });

            if (window.targetData) {
                document.querySelector('[data-version="revision"]').classList.add('has-data');
            }

            compareData();
            showToast('文件上传成功', 'success');
        })
        .catch(error => {
            showToast('文件解析失败: ' + error.message, 'error');
        });
}

// 测试数据1（版本1）- 包含多记录模块示例
function getTestData1() {
    return {
        "报告基础信息": {
            "报告分类": "严重不良事件（SAE）",
            "报告模块": "报告基础信息",
            "报告编号": "SAE-001",
            "首次获知日期": "2024-01-15"
        },
        "受试者信息": {
            "报告模块": "受试者信息",
            "受试者筛选号": "S001",
            "受试者随机号": "R001",
            "出生日期": "1980-05-20",
            "性别": "男"
        },
        "试验用药信息": {
            "报告模块": "试验用药信息",
            "用药名称": "试验药物A",
            "剂量": "100mg",
            "给药途径": "口服"
        },
        "SAE/ECI/AESI的详细情况": [
            {
                "报告分类": "报告",
                "报告模块": "SAE/ECI/AESI的详细情况",
                "不良事件名称": "呕吐",
                "严重性标准": "需要住院",
                "严重程度": "",
                "CTCAE分级": "2级",
                "发生日期": "2025-06-05",
                "事件转归": "未恢复/未治愈",
                "结束日期": "",
                "AESI": "",
                "ECI": "",
                "药物信息": [
                    {
                        "试验用药品名称": "注射用SKB315",
                        "与试验用药品的关系": "可能有关"
                    }
                ]
            }
        ]
    };
}

// 测试数据2（版本2）- 包含多记录模块示例
function getTestData2() {
    return {
        "报告基础信息": {
            "报告分类": "严重不良事件（SAE）",
            "报告模块": "报告基础信息",
            "报告编号": "SAE-001-V2",  // 修改
            "首次获知日期": "2024-01-15",
            "报告状态": "已审核"  // 新增
        },
        "受试者信息": {
            "报告模块": "受试者信息",
            "受试者筛选号": "S001",
            "受试者随机号": "R001-UPDATED",  // 修改
            "出生日期": "1980-05-20",
            "性别": "男",
            "体重": "70kg"  // 新增
        },
        "试验用药信息": {
            "报告模块": "试验用药信息",
            "用药名称": "试验药物A",
            "剂量": "200mg",  // 修改
            "给药途径": "静脉注射",  // 修改
            "用药频次": "每日一次"  // 新增
        },
        "SAE/ECI/AESI的详细情况": [
            {
                "报告分类": "报告",
                "报告模块": "SAE/ECI/AESI的详细情况",
                "不良事件名称": "呕吐",
                "严重性标准": "需要住院",
                "严重程度": "",
                "CTCAE分级": "2级",
                "发生日期": "2025-06-05",
                "事件转归": "未恢复/未治愈",
                "结束日期": "",
                "AESI": "",
                "ECI": "",
                "药物信息": [
                    {
                        "试验用药品名称": "注射用SKB315",
                        "与试验用药品的关系": "可能有关"
                    }
                ]
            },
            {
                "报告分类": "报告",
                "报告模块": "SAE/ECI/AESI的详细情况",
                "不良事件名称": "呕吐",
                "严重性标准": "需要住院",
                "严重程度": "",
                "CTCAE分级": "1级",  // 降级了
                "发生日期": "2025-06-05",
                "事件转归": "恢复中/治愈中",  // 好转了
                "结束日期": "2025-06-07",  // 有结束日期了
                "AESI": "",
                "ECI": "",
                "药物信息": [
                    {
                        "试验用药品名称": "注射用SKB315",
                        "与试验用药品的关系": "可能有关"
                    }
                ]
            }
        ]
    };
}

// 测试数据3（版本3）- 包含多记录模块示例
function getTestData3() {
    return {
        "报告基础信息": {
            "报告分类": "严重不良事件（SAE）",
            "报告模块": "报告基础信息",
            "报告编号": "SAE-001",  // 保持版本1的值
            "首次获知日期": "2024-01-15"
            // 缺少"报告状态"字段
        },
        "受试者信息": {
            "报告模块": "受试者信息",
            "受试者筛选号": "S001",
            "受试者随机号": "R001",  // 保持版本1的值
            "出生日期": "1980-05-20",
            "性别": "男",
            "体重": "70kg"  // 已经更新到版本2的值
        },
        "试验用药信息": {
            "报告模块": "试验用药信息",
            "用药名称": "试验药物A",
            "剂量": "100mg",  // 保持版本1的值
            "给药途径": "口服"  // 保持版本1的值
            // 缺少"用药频次"字段
        },
        "SAE/ECI/AESI的详细情况": [
            {
                "报告分类": "报告",
                "报告模块": "SAE/ECI/AESI的详细情况",
                "不良事件名称": "呕吐",
                "严重性标准": "需要住院",
                "严重程度": "",
                "CTCAE分级": "2级",
                "发生日期": "2025-06-05",
                "事件转归": "未恢复/未治愈",
                "结束日期": "",
                "AESI": "",
                "ECI": "",
                "药物信息": [
                    {
                        "试验用药品名称": "注射用SKB315",
                        "与试验用药品的关系": "可能有关"
                    }
                ]
            }
            // 版本3只有一条记录，没有合并版本2的第二条记录
        ]
    };
}