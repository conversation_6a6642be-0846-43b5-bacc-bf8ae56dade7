// 存储管理器
class StorageManager {
    constructor() {
        this.storage = {
            currentData: null,
            versions: [],
            moduleIndexConfig: {
                '报告基础信息': [],
                '报告类型': [],
                '受试者信息': ['受试者筛选号', '受试者随机号'],
                'SAE/ECI/AESI的详细情况': ['不良事件名称', '发生日期'],
                '受试者死亡、住院信息': [],
                'SAE/ECI/AESI的描述': [],
                '其他导致该SAE/ECI/AESI的可能因素': ['事件名称'],
                '试验用药信息': {
                    '试验用药基础信息': [],
                    '试验用药列表信息': ['试验用药名称'],
                    '剂量信息': ['该剂量用药开始日期', '试验用药剂量数值']
                },
                '与事件相关的实验室检查': ['检查项目名称', '检查日期'],
                '与事件相关的现病史': ['疾病名称'],
                '与事件相关的既往病史': ['疾病名称'],
                '合并用药信息': ['合并用药品名称', '合并用药开始日期'],
                '治疗用药信息': ['治疗用药品名称', '治疗用药开始日期'],
                '报告者信息': [],
                '既往用药信息': ['既往用药品名称'],
                'AE': ['AE名称']
            },
            currentFilter: 'all',
            currentMode: 'diff',
            revisions: new Map(), // 存储修订状态
            revisionFilter: 'all',
            acceptedChanges: new Map(), // 存储对比模式下接受的更改
            rejectedChanges: new Map(), // 存储对比模式下拒绝的更改
            dataVersion: 0, // 数据版本，用于检测数据是否变化
            operationHistory: [] // 操作历史
        };
    }

    get(key) {
        if (key === 'dataVersion' && this.storage[key] === undefined) {
            this.storage[key] = 0;
        }
        if (key === 'acceptedChanges' && !this.storage[key]) {
            return new Map();
        }
        if (key === 'rejectedChanges' && !this.storage[key]) {
            return new Map();
        }
        return this.storage[key];
    }

    set(key, value) {
        this.storage[key] = value;
    }

    getVersions() {
        return this.storage.versions || [];
    }

    addVersion(data) {
        if (!this.storage.versions) {
            this.storage.versions = [];
        }
        const version = {
            id: Date.now(),
            timestamp: new Date().toISOString(),
            data: JSON.parse(JSON.stringify(data)),
            name: `版本 ${this.storage.versions.length + 1}`
        };
        this.storage.versions.push(version);
        // 保留最近10个版本
        if (this.storage.versions.length > 10) {
            this.storage.versions.shift();
        }
        return version;
    }

    getModuleIndexConfig() {
        return this.storage.moduleIndexConfig || {};
    }

    setModuleIndexConfig(config) {
        this.storage.moduleIndexConfig = config;
    }

    // 修订管理
    setRevisionStatus(path, status) {
        const oldStatus = this.storage.revisions.get(path);
        this.storage.revisions.set(path, status);

        // 记录状态变化用于历史记录
        return { oldStatus: oldStatus || 'pending', newStatus: status };
    }

    getRevisionStatus(path) {
        return this.storage.revisions.get(path) || null;
    }

    clearRevisions() {
        this.storage.revisions.clear();
    }

    getAllRevisions() {
        return this.storage.revisions;
    }

    // 对比模式更改管理
    setChangeStatus(path, status) {
        if (status === 'accepted') {
            this.storage.acceptedChanges.set(path, true);
            this.storage.rejectedChanges.delete(path);
        } else if (status === 'rejected') {
            this.storage.rejectedChanges.set(path, true);
            this.storage.acceptedChanges.delete(path);
        }
    }

    isChangeAccepted(path) {
        return this.storage.acceptedChanges.has(path);
    }

    isChangeRejected(path) {
        return this.storage.rejectedChanges.has(path);
    }

    clearChangeStatus() {
        this.storage.acceptedChanges.clear();
        this.storage.rejectedChanges.clear();
    }

    // 操作历史管理
    addHistory(operation) {
        if (!this.storage.operationHistory) {
            this.storage.operationHistory = [];
        }
        const historyItem = {
            id: Date.now(),
            timestamp: new Date().toISOString(),
            ...operation
        };
        this.storage.operationHistory.unshift(historyItem);
        // 保留最近100条历史
        if (this.storage.operationHistory.length > 100) {
            this.storage.operationHistory.pop();
        }
        return historyItem;
    }

    getHistory() {
        return this.storage.operationHistory || [];
    }

    clearHistory() {
        this.storage.operationHistory = [];
    }
}

// 创建全局存储实例
const storage = new StorageManager();