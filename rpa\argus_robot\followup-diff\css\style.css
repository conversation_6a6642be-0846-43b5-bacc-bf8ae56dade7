* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary: #6366f1;
    --primary-dark: #4f46e5;
    --secondary: #8b5cf6;
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;
    --info: #3b82f6;
    --dark: #1f2937;
    --light: #f3f4f6;
    --white: #ffffff;
    --added: #22c55e;
    --added-bg: #dcfce7;
    --removed: #ef4444;
    --removed-bg: #fee2e2;
    --modified: #f59e0b;
    --modified-bg: #fef3c7;
    --revision: #3b82f6;
    --revision-bg: #dbeafe;
    --shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e <PERSON>', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    color: var(--dark);
}

.app-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

.app-header {
    background: var(--white);
    border-radius: 16px;
    padding: 24px;
    box-shadow: var(--shadow);
    margin-bottom: 24px;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 20px;
}

.app-title {
    font-size: 28px;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    display: flex;
    align-items: center;
    gap: 12px;
}

.mode-indicator {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: var(--revision-bg);
    color: var(--revision);
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    margin-left: 20px;
}

.mode-indicator.diff-mode {
    background: var(--light);
    color: var(--dark);
}

.version-control {
    display: flex;
    gap: 12px;
    align-items: center;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    transition: left 0.3s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    color: var(--white);
    box-shadow: 0 4px 6px rgba(99, 102, 241, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(99, 102, 241, 0.4);
}

.btn-secondary {
    background: var(--light);
    color: var(--dark);
    border: 2px solid var(--light);
}

.btn-secondary:hover {
    background: var(--white);
    border-color: var(--primary);
}

.btn-success {
    background: linear-gradient(135deg, var(--success) 0%, #059669 100%);
    color: var(--white);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning) 0%, #d97706 100%);
    color: var(--white);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger) 0%, #dc2626 100%);
    color: var(--white);
}

.btn-info {
    background: linear-gradient(135deg, var(--info) 0%, #2563eb 100%);
    color: var(--white);
}

.data-source-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

/* 版本选择器 */
.version-selector {
    background: var(--white);
    border-radius: 16px;
    padding: 20px;
    box-shadow: var(--shadow);
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 16px;
}

.version-tabs {
    display: flex;
    gap: 8px;
    background: var(--light);
    padding: 4px;
    border-radius: 12px;
}

.version-tab {
    padding: 8px 20px;
    border: none;
    background: transparent;
    color: var(--dark);
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
}

.version-tab.active {
    background: var(--white);
    color: var(--primary);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.version-tab.has-data::after {
    content: '';
    position: absolute;
    top: 6px;
    right: 6px;
    width: 6px;
    height: 6px;
    background: var(--success);
    border-radius: 50%;
}

/* 过滤器样式 */
.filter-section {
    background: var(--white);
    border-radius: 16px;
    padding: 20px;
    box-shadow: var(--shadow);
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 16px;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 12px;
}

.filter-label {
    font-weight: 600;
    color: #6b7280;
}

.filter-buttons {
    display: flex;
    gap: 8px;
}

.filter-btn {
    padding: 6px 16px;
    border: 2px solid var(--light);
    background: var(--white);
    color: #6b7280;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    transition: var(--transition);
}

.filter-btn.active {
    border-color: var(--primary);
    background: var(--primary);
    color: var(--white);
}

.filter-btn:hover:not(.active) {
    border-color: var(--primary);
    color: var(--primary);
}

/* 修订模式过滤器 */
.revision-filter-btn {
    padding: 6px 16px;
    border: 2px solid var(--light);
    background: var(--white);
    color: #6b7280;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    transition: var(--transition);
}

.revision-filter-btn.active {
    border-color: var(--revision);
    background: var(--revision);
    color: var(--white);
}

/* 主内容区 - 单列布局 */
.main-content {
    background: var(--white);
    border-radius: 16px;
    box-shadow: var(--shadow);
    overflow: hidden;
}

.content-header {
    padding: 20px 24px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid var(--light);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.content-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--dark);
}

.content-body {
    max-height: 70vh;
    overflow-y: auto;
}

/* 模块样式 */
.module-section {
    border-bottom: 1px solid var(--light);
}

.module-section:last-child {
    border-bottom: none;
}

.module-header {
    padding: 16px 24px;
    background: #f8f9fa;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: var(--transition);
    position: sticky;
    top: 0;
    z-index: 10;
}

.module-header:hover {
    background: #e9ecef;
}

.module-header.has-changes {
    background: linear-gradient(to right, #f8f9fa, rgba(99, 102, 241, 0.1));
}

.module-header.has-revisions {
    background: linear-gradient(to right, #f8f9fa, rgba(59, 130, 246, 0.1));
}

.module-title {
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.module-arrow {
    transition: transform 0.3s;
    color: #6b7280;
}

.module-section.collapsed .module-arrow {
    transform: rotate(-90deg);
}

.module-stats {
    display: flex;
    gap: 12px;
    font-size: 12px;
}

.stat-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 600;
}

.stat-added {
    background: var(--added-bg);
    color: var(--added);
}

.stat-removed {
    background: var(--removed-bg);
    color: var(--removed);
}

.stat-modified {
    background: var(--modified-bg);
    color: var(--modified);
}

.stat-revision {
    background: var(--revision-bg);
    color: var(--revision);
}

.stat-info {
    background: #e0f2fe;
    color: var(--info);
}

.module-content {
    padding: 24px;
    display: none;
}

.module-section:not(.collapsed) .module-content {
    display: block;
}

/* Word修订样式 */
.field-row {
    padding: 12px 0;
    border-bottom: 1px solid #f3f4f6;
    display: flex;
    align-items: flex-start;
    gap: 16px;
}

.field-row:last-child {
    border-bottom: none;
}

.field-label {
    font-weight: 600;
    color: #4b5563;
    min-width: 200px;
    flex-shrink: 0;
}

.field-value-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* 修订样式 */
.revision-item {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 4px 0;
}

.revision-added {
    color: var(--added);
    background: var(--added-bg);
    padding: 2px 8px;
    border-radius: 4px;
    border-left: 3px solid var(--added);
}

.revision-removed {
    color: var(--removed);
    background: var(--removed-bg);
    padding: 2px 8px;
    border-radius: 4px;
    text-decoration: line-through;
    border-left: 3px solid var(--removed);
    opacity: 0.8;
}

.revision-modified {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.revision-old {
    color: var(--removed);
    text-decoration: line-through;
    opacity: 0.7;
    background: var(--removed-bg);
    padding: 2px 8px;
    border-radius: 4px;
}

.revision-new {
    color: var(--added);
    background: var(--added-bg);
    padding: 2px 8px;
    border-radius: 4px;
}

/* 修订模式样式 */
.revision-suggestion {
    background: var(--revision-bg);
    border: 1px solid var(--revision);
    padding: 8px 12px;
    border-radius: 6px;
    margin: 4px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
}

.revision-suggestion.accepted {
    background: var(--added-bg);
    border-color: var(--added);
    opacity: 0.9;
}

.revision-suggestion.rejected {
    background: var(--removed-bg);
    border-color: var(--removed);
    opacity: 0.7;
}

.revision-suggestion.up-to-date {
    background: #f0f9ff;
    border-color: #0ea5e9;
}

.revision-content {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8px;
}

.revision-type {
    font-size: 12px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.1);
}

.revision-value {
    display: flex;
    align-items: center;
    gap: 8px;
}

.revision-arrow {
    color: #6b7280;
    font-size: 12px;
    margin: 0 4px;
}

.up-to-date-badge {
    background: #0ea5e9;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    margin-left: 8px;
}

/* 数组样式 */
.array-container {
    margin-top: 8px;
    padding-left: 20px;
    border-left: 2px solid var(--light);
}

.array-item {
    margin-bottom: 16px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    position: relative;
}

.array-item.added {
    background: var(--added-bg);
    border: 1px solid var(--added);
}

.array-item.removed {
    background: var(--removed-bg);
    border: 1px solid var(--removed);
    opacity: 0.8;
}

.array-item.modified {
    background: var(--modified-bg);
    border: 1px solid var(--modified);
}

.array-item-header {
    font-weight: 600;
    margin-bottom: 8px;
    color: #374151;
}

/* 操作按钮 */
.revision-actions {
    display: flex;
    gap: 8px;
    margin-left: auto;
}

.module-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 6px;
    cursor: pointer;
    border: none;
    transition: var(--transition);
}

.action-btn:hover {
    transform: scale(1.05);
}

.accept-btn {
    background: var(--success);
    color: var(--white);
}

.reject-btn {
    background: var(--danger);
    color: var(--white);
}

.undo-btn {
    background: var(--warning);
    color: var(--white);
}

.revision-btn {
    background: var(--revision);
    color: var(--white);
}

/* 配置面板 */
.config-panel {
    background: var(--white);
    border-radius: 16px;
    box-shadow: var(--shadow);
    margin-bottom: 24px;
    overflow: hidden;
}

.config-panel-header {
    padding: 20px 24px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: var(--transition);
}

.config-panel-header:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.config-panel-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--dark);
    display: flex;
    align-items: center;
    gap: 12px;
}

.config-panel-arrow {
    transition: transform 0.3s;
    color: #6b7280;
}

.config-panel.collapsed .config-panel-arrow {
    transform: rotate(-90deg);
}

.config-panel-body {
    padding: 24px;
    transition: max-height 0.3s ease-out, padding 0.3s ease-out;
    max-height: 400px;
    overflow: hidden;
}

.config-panel.collapsed .config-panel-body {
    max-height: 0;
    padding: 0 24px;
}

#moduleConfigs {
    max-height: 350px;
    overflow-y: auto;
    overflow-x: hidden;
    padding-right: 10px;
}

/* 自定义滚动条样式 */
#moduleConfigs::-webkit-scrollbar,
.content-body::-webkit-scrollbar {
    width: 8px;
}

#moduleConfigs::-webkit-scrollbar-track,
.content-body::-webkit-scrollbar-track {
    background: var(--light);
    border-radius: 4px;
}

#moduleConfigs::-webkit-scrollbar-thumb,
.content-body::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
    transition: background 0.3s;
}

#moduleConfigs::-webkit-scrollbar-thumb:hover,
.content-body::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

.module-config {
    margin-bottom: 16px;
    padding: 0;
    background: #f8f9fa;
    border-radius: 8px;
    overflow: hidden;
}

.module-config-header {
    font-weight: 600;
    padding: 12px 16px;
    color: #374151;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #e5e7eb;
    transition: var(--transition);
}

.module-config-header:hover {
    background: #d1d5db;
}

.module-config-arrow {
    transition: transform 0.3s;
    color: #6b7280;
}

.module-config.collapsed .module-config-arrow {
    transform: rotate(-90deg);
}

.module-config-body {
    padding: 12px 16px;
    display: block;
}

.module-config.collapsed .module-config-body {
    display: none;
}

.tag-input {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding: 10px;
    border: 2px solid var(--light);
    border-radius: 10px;
    min-height: 50px;
    align-items: center;
}

.tag {
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    color: var(--white);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
    animation: tagIn 0.3s ease-out;
}

@keyframes tagIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.tag-remove {
    cursor: pointer;
    font-weight: bold;
    opacity: 0.8;
    transition: opacity 0.2s;
}

.tag-remove:hover {
    opacity: 1;
}

/* 状态栏 */
.status-bar {
    background: var(--white);
    border-radius: 16px;
    padding: 16px 24px;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
}

.status-info {
    display: flex;
    gap: 24px;
    align-items: center;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.status-added {
    background: var(--added);
}

.status-removed {
    background: var(--removed);
}

.status-modified {
    background: var(--modified);
}

.status-revision {
    background: var(--revision);
}

.status-info {
    background: var(--info);
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
}

.empty-state-icon {
    font-size: 64px;
    margin-bottom: 16px;
    opacity: 0.3;
}

.empty-state-text {
    font-size: 18px;
    margin-bottom: 24px;
}

.no-changes-message {
    padding: 40px;
    text-align: center;
    color: #6b7280;
}

.no-changes-icon {
    display: block;
    font-size: 48px;
    margin-bottom: 12px;
    opacity: 0.5;
    color: var(--success);
}

.no-changes-text {
    font-size: 16px;
    color: #6b7280;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 1000;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--white);
    border-radius: 16px;
    padding: 32px;
    max-width: 1200px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -40%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.modal-title {
    font-size: 24px;
    font-weight: 700;
    color: var(--dark);
}

.modal-close {
    font-size: 24px;
    cursor: pointer;
    color: var(--dark);
    opacity: 0.6;
    transition: opacity 0.2s;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
}

.modal-close:hover {
    opacity: 1;
    background: var(--light);
}

.json-input-container {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
}

.json-input-section {
    display: flex;
    flex-direction: column;
}

.json-textarea {
    width: 100%;
    min-height: 400px;
    padding: 16px;
    border: 2px solid var(--light);
    border-radius: 10px;
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: 13px;
    resize: vertical;
    transition: var(--transition);
}

.json-textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.json-input-label {
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--dark);
    display: flex;
    align-items: center;
    gap: 8px;
}

.version-badge {
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    background: var(--light);
    color: var(--dark);
}

.version-badge.primary {
    background: var(--primary);
    color: var(--white);
}

.json-input-actions {
    margin-top: 24px;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

/* 修订摘要 */
.revision-summary {
    background: var(--revision-bg);
    border: 1px solid var(--revision);
    border-radius: 12px;
    padding: 16px 20px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.revision-summary-info {
    display: flex;
    align-items: center;
    gap: 16px;
}

.revision-summary-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.revision-summary-actions {
    display: flex;
    gap: 12px;
}

/* 操作历史 */
.history-item {
    padding: 12px 16px;
    border-bottom: 1px solid var(--light);
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: background 0.2s;
}

.history-item:hover {
    background: #f8f9fa;
}

.history-info {
    flex: 1;
}

.history-type {
    font-weight: 600;
    color: var(--dark);
    margin-bottom: 4px;
}

.history-detail {
    font-size: 12px;
    color: #6b7280;
}

.history-time {
    font-size: 12px;
    color: #9ca3af;
    margin-left: 16px;
}

.history-actions {
    display: flex;
    gap: 8px;
}

/* Toast提示 */
.toast {
    position: fixed;
    bottom: 24px;
    right: 24px;
    background: var(--white);
    padding: 16px 24px;
    border-radius: 12px;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 12px;
    transform: translateX(400px);
    transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    z-index: 2000;
}

.toast.show {
    transform: translateX(0);
}

.toast-icon {
    font-size: 20px;
}

.toast-success .toast-icon {
    color: var(--success);
}

.toast-error .toast-icon {
    color: var(--danger);
}

.toast-warning .toast-icon {
    color: var(--warning);
}

.toast-info .toast-icon {
    color: var(--info);
}

/* 快捷键提示 */
.shortcuts {
    position: fixed;
    bottom: 24px;
    left: 24px;
    background: var(--dark);
    color: var(--white);
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 12px;
    opacity: 0.8;
    transition: opacity 0.3s;
}

.shortcuts:hover {
    opacity: 1;
}

.shortcut-key {
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 6px;
    border-radius: 4px;
    margin: 0 4px;
}

.loader {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid var(--light);
    border-top-color: var(--primary);
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 历史记录样式 */
.history-container {
    max-height: 60vh;
    overflow-y: auto;
}

.history-empty {
    text-align: center;
    padding: 40px;
    color: #6b7280;
}

.history-type-icon {
    display: inline-block;
    margin-right: 8px;
}

/* 版本历史样式 */
.version-list {
    max-height: 60vh;
    overflow-y: auto;
}

.version-item {
    padding: 16px;
    border-bottom: 1px solid var(--light);
    cursor: pointer;
    transition: background 0.2s;
}

.version-item:hover {
    background: #f8f9fa;
}

.version-item:last-child {
    border-bottom: none;
}

.version-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.version-name {
    font-weight: 600;
    color: var(--dark);
}

.version-time {
    font-size: 12px;
    color: #6b7280;
}

.version-details {
    font-size: 14px;
    color: #6b7280;
}

.version-actions {
    margin-top: 12px;
    display: flex;
    gap: 8px;
}

@media (max-width: 1024px) {
    .json-input-container {
        grid-template-columns: 1fr;
    }

    .field-row {
        flex-direction: column;
        gap: 8px;
    }

    .field-label {
        min-width: auto;
    }
}