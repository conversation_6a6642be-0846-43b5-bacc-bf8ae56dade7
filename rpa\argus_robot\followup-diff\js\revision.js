// 使函数全局可用（用于调试）
window.applySimpleFieldRevision = applySimpleFieldRevision;
window.applyRevisionToData = applyRevisionToData;// revision.js - 修订相关功能

window.revisionStats = { pending: 0, accepted: 0, rejected: 0, upToDate: 0 };
window.revisionSuggestions = new Map(); // 存储所有修订建议的详细信息

// 显示修订模式
function showRevisionMode() {
    if (!window.leftData || !window.rightData || !window.targetData) {
        showToast('需要所有三个版本的数据才能进入修订模式', 'warning');
        return;
    }

    // 检查是否有任何接受的更改
    const acceptedChanges = storage.get('acceptedChanges');
    if (acceptedChanges.size === 0) {
        showToast('提示：请先在对比模式下接受需要的更改，然后再进入修订模式', 'info');
    }

    window.revisionStats = { pending: 0, accepted: 0, rejected: 0, upToDate: 0 };

    // 清空之前的修订建议（避免累积）
    window.revisionSuggestions.clear();

    // 清理数据
    const cleanedLeftData = cleanObject(window.leftData);
    const cleanedRightData = cleanObject(window.rightData);
    const cleanedTargetData = cleanObject(window.targetData);

    // 按模块组织数据
    const leftModules = organizeByModules(cleanedLeftData);
    const rightModules = organizeByModules(cleanedRightData);
    const targetModules = organizeByModules(cleanedTargetData);

    // 渲染修订模式
    renderRevisionMode(leftModules, rightModules, targetModules);
}

// 渲染修订模式
function renderRevisionMode(leftModules, rightModules, targetModules) {
    const container = document.getElementById('mainContent');
    let html = '';
    let hasAnyRevisions = false;
    let totalRevisions = 0;

    // 重置 revisionStats 计数器
    window.revisionStats = { pending: 0, accepted: 0, rejected: 0, upToDate: 0 };

    // 清空之前的修订建议
    window.revisionSuggestions.clear();

    // 获取所有模块名
    const allModules = new Set([...Object.keys(leftModules), ...Object.keys(rightModules), ...Object.keys(targetModules)]);

    console.log('开始生成修订建议，模块数:', allModules.size);

    for (const moduleName of allModules) {
        const module1Data = leftModules[moduleName] || {};
        const module2Data = rightModules[moduleName] || {};
        const module3Data = targetModules[moduleName] || {};

        console.log(`处理模块: ${moduleName}`, {
            module1Keys: Object.keys(module1Data).length,
            module2Keys: Object.keys(module2Data).length,
            module3Keys: Object.keys(module3Data).length
        });

        const revisions = generateRevisions(module1Data, module2Data, module3Data, moduleName);

        if (revisions && revisions.length > 0) {
            hasAnyRevisions = true;
            totalRevisions += revisions.length;
            html += renderRevisionModule(moduleName, revisions);
            console.log(`模块 ${moduleName} 生成了 ${revisions.length} 个修订建议`);
        }
    }

    if (!hasAnyRevisions) {
        console.log('没有生成任何修订建议');

        // 检查是否有接受的更改
        const acceptedChanges = storage.get('acceptedChanges');
        if (acceptedChanges.size === 0) {
            html = `
                <div class="empty-state">
                    <div class="empty-state-icon">💡</div>
                    <div class="empty-state-text">
                        <h3 style="margin-bottom: 16px;">没有发现需要的修订</h3>
                        <p style="color: #6b7280; line-height: 1.6;">
                            请先在"对比模式"下查看版本1和版本2的差异，<br>
                            接受您需要的更改，然后再切换到修订模式。<br>
                            只有已接受的更改才会作为修订建议应用到版本3。
                        </p>
                        <button class="btn btn-primary" style="margin-top: 20px;" onclick="switchMode('diff')">
                            返回对比模式
                        </button>
                    </div>
                </div>
            `;
        } else {
            html = '<div class="empty-state"><div class="empty-state-icon">✓</div><div class="empty-state-text">版本3已是最新状态，无需修订</div></div>';
        }
    } else {
        // 在顶部添加提示信息
        html = `
            <div style="padding: 16px 24px; background: var(--revision-bg); border-radius: 8px; margin-bottom: 16px;">
                <p style="color: var(--revision); font-weight: 600;">
                    发现 ${totalRevisions} 个修订建议，这些是基于您在对比模式下接受的更改。
                </p>
            </div>
        ` + html;
    }

    container.innerHTML = html;

    // 应用过滤器
    applyRevisionFilter();

    // 更新统计数据
    updateRevisionStats();
}

// 生成修订建议
function generateRevisions(data1, data2, data3, moduleName) {
    const revisions = [];

    console.log(`生成模块 ${moduleName} 的修订建议`);
    console.log('模块数据:', { data1, data2, data3 });

    // 检查数据是否直接就是模块的内容
    const isModuleData = data1 && data1['报告模块'] === moduleName;

    // 递归生成所有层级的修订建议
    function processData(d1, d2, d3, prefix = '') {
        const allKeys = new Set([...Object.keys(d1 || {}), ...Object.keys(d2 || {}), ...Object.keys(d3 || {})]);

        for (const key of allKeys) {
            if (key === undefined || key === 'undefined' || key === '报告分类' || key === '报告模块') continue;

            const fullPath = prefix ? `${prefix}.${key}` : key;
            // 修复：正确构建changePath，避免重复的模块名
            const changePath = `${moduleName}.${fullPath}`;

            const value1 = d1 ? d1[key] : undefined;
            const value2 = d2 ? d2[key] : undefined;
            const value3 = d3 ? d3[key] : undefined;

            // 打印调试信息
            if (key === '收到报告日期' || fullPath.includes('收到报告日期')) {
                console.log('处理收到报告日期字段:', {
                    key,
                    fullPath,
                    changePath,
                    value1,
                    value2,
                    value3,
                    prefix,
                    moduleName,
                    isModuleData
                });
            }

            // 先检查当前层级是否有变化
            const changeFrom1To2 = analyzeChange(value1, value2);

            if (changeFrom1To2.hasChange) {
                // 只有在对比模式下接受的更改才生成修订建议
                if (storage.isChangeAccepted(changePath)) {
                    const shouldRevise = shouldApplyRevision(value1, value2, value3);
                    const isUpToDate = JSON.stringify(value2) === JSON.stringify(value3);

                    // 即使是最新的，也要显示，让用户知道
                    if (shouldRevise || isUpToDate) {
                        console.log(`发现需要修订的字段: ${fullPath}`, {
                            type: changeFrom1To2.type,
                            value1,
                            value2,
                            value3,
                            isUpToDate,
                            changePath
                        });

                        // 修复：使用fullPath作为显示的key
                        const displayKey = fullPath;

                        revisions.push({
                            key: displayKey,
                            type: changeFrom1To2.type,
                            suggestion: value2,
                            currentValue: value3,
                            originalValue: value1,
                            path: changePath,
                            isUpToDate: isUpToDate
                        });

                        // 存储修订建议的详细信息
                        window.revisionSuggestions.set(changePath, {
                            type: changeFrom1To2.type,
                            suggestion: value2,
                            currentValue: value3,
                            originalValue: value1,
                            fullPath: displayKey,
                            moduleName: moduleName,
                            isUpToDate: isUpToDate,
                            parentPath: prefix // 添加父路径信息
                        });

                        // 统计状态
                        const currentStatus = storage.getRevisionStatus(changePath);
                        if (isUpToDate) {
                            window.revisionStats.upToDate++;
                        } else if (!currentStatus || currentStatus === 'pending') {
                            storage.setRevisionStatus(changePath, 'pending');
                            window.revisionStats.pending++;
                        } else if (currentStatus === 'accepted') {
                            window.revisionStats.accepted++;
                        } else if (currentStatus === 'rejected') {
                            window.revisionStats.rejected++;
                        }
                    }
                }
            }

            // 处理嵌套对象 - 只有当值是对象且不是数组时才递归
            // 重要：如果整个对象已经被处理为修订，不要再递归处理其内部字段
            if (!changeFrom1To2.hasChange || !storage.isChangeAccepted(changePath)) {
                if (typeof value1 === 'object' && !Array.isArray(value1) && value1 !== null ||
                    typeof value2 === 'object' && !Array.isArray(value2) && value2 !== null ||
                    typeof value3 === 'object' && !Array.isArray(value3) && value3 !== null) {

                    // 递归处理嵌套对象的字段
                    processData(value1, value2, value3, fullPath);
                }
            }
            // 处理数组
            else if (Array.isArray(value1) || Array.isArray(value2) || Array.isArray(value3)) {
                processArrayRevisions(value1 || [], value2 || [], value3 || [], fullPath);
            }
        }
    }

    // 处理数组修订
    function processArrayRevisions(arr1, arr2, arr3, prefix) {
        const indexFields = getIndexFieldsForModule(moduleName);

        if (indexFields.length > 0) {
            // 基于索引字段的匹配
            const map1 = new Map();
            const map2 = new Map();
            const map3 = new Map();

            // 建立索引映射
            arr1.forEach(item => {
                const key = indexFields.map(f => item[f]).filter(v => v).join('|');
                if (key) map1.set(key, item);
            });

            arr2.forEach(item => {
                const key = indexFields.map(f => item[f]).filter(v => v).join('|');
                if (key) map2.set(key, item);
            });

            arr3.forEach(item => {
                const key = indexFields.map(f => item[f]).filter(v => v).join('|');
                if (key) map3.set(key, item);
            });

            // 检查需要修订的项
            const allKeys = new Set([...map1.keys(), ...map2.keys(), ...map3.keys()]);

            for (const key of allKeys) {
                const item1 = map1.get(key);
                const item2 = map2.get(key);
                const item3 = map3.get(key);
                const changePath = `${moduleName}.${prefix}[${key}]`;

                const changeFrom1To2 = analyzeChange(item1, item2);

                if (changeFrom1To2.hasChange && storage.isChangeAccepted(changePath)) {
                    const shouldRevise = shouldApplyRevision(item1, item2, item3);
                    const isUpToDate = JSON.stringify(item2) === JSON.stringify(item3);

                    if (shouldRevise || isUpToDate) {
                        revisions.push({
                            key: `${prefix}[${key}]`,
                            type: changeFrom1To2.type,
                            suggestion: item2,
                            currentValue: item3,
                            originalValue: item1,
                            path: changePath,
                            isUpToDate: isUpToDate
                        });

                        // 统计状态
                        const currentStatus = storage.getRevisionStatus(changePath);
                        if (isUpToDate) {
                            window.revisionStats.upToDate++;
                        } else if (!currentStatus || currentStatus === 'pending') {
                            storage.setRevisionStatus(changePath, 'pending');
                            window.revisionStats.pending++;
                        } else if (currentStatus === 'accepted') {
                            window.revisionStats.accepted++;
                        } else if (currentStatus === 'rejected') {
                            window.revisionStats.rejected++;
                        }
                    }
                }

                // 递归处理数组项中的对象
                if (item1 || item2 || item3) {
                    processData(item1, item2, item3, `${prefix}[${key}]`);
                }
            }
        } else {
            // 基于位置的匹配
            const maxLen = Math.max(arr1.length, arr2.length, arr3.length);

            for (let i = 0; i < maxLen; i++) {
                const item1 = arr1[i];
                const item2 = arr2[i];
                const item3 = arr3[i];
                const changePath = `${moduleName}.${prefix}[${i}]`;

                const changeFrom1To2 = analyzeChange(item1, item2);

                if (changeFrom1To2.hasChange && storage.isChangeAccepted(changePath)) {
                    const shouldRevise = shouldApplyRevision(item1, item2, item3);
                    const isUpToDate = JSON.stringify(item2) === JSON.stringify(item3);

                    if (shouldRevise || isUpToDate) {
                        revisions.push({
                            key: `${prefix}[${i}]`,
                            type: changeFrom1To2.type,
                            suggestion: item2,
                            currentValue: item3,
                            originalValue: item1,
                            path: changePath,
                            isUpToDate: isUpToDate
                        });

                        // 统计状态
                        const currentStatus = storage.getRevisionStatus(changePath);
                        if (isUpToDate) {
                            window.revisionStats.upToDate++;
                        } else if (!currentStatus || currentStatus === 'pending') {
                            storage.setRevisionStatus(changePath, 'pending');
                            window.revisionStats.pending++;
                        } else if (currentStatus === 'accepted') {
                            window.revisionStats.accepted++;
                        } else if (currentStatus === 'rejected') {
                            window.revisionStats.rejected++;
                        }
                    }
                }

                // 递归处理数组项中的对象
                if (item1 || item2 || item3) {
                    processData(item1, item2, item3, `${prefix}[${i}]`);
                }
            }
        }
    }

    // 开始处理模块数据
    processData(data1, data2, data3);

    console.log(`模块 ${moduleName} 生成了 ${revisions.length} 个修订建议`);
    return revisions;
}

// 渲染修订模块
function renderRevisionModule(moduleName, revisions) {
    const pendingCount = revisions.filter(r => {
        const status = storage.getRevisionStatus(r.path);
        return (!status || status === 'pending') && !r.isUpToDate;
    }).length;
    const acceptedCount = revisions.filter(r => storage.getRevisionStatus(r.path) === 'accepted').length;
    const rejectedCount = revisions.filter(r => storage.getRevisionStatus(r.path) === 'rejected').length;
    const upToDateCount = revisions.filter(r => r.isUpToDate).length;

    const hasPendingRevisions = pendingCount > 0;

    let html = `
        <div class="module-section ${hasPendingRevisions ? '' : 'collapsed'}" data-module="${moduleName}">
            <div class="module-header has-revisions" onclick="toggleModule(this)">
                <div class="module-title">
                    <span class="module-arrow">▼</span>
                    ${moduleName}
                </div>
                <div style="display: flex; align-items: center; gap: 16px;">
                    <div class="module-stats">
                        ${pendingCount > 0 ? `<span class="stat-badge stat-revision">${pendingCount} 待处理</span>` : ''}
                        ${acceptedCount > 0 ? `<span class="stat-badge stat-added">${acceptedCount} 已接受</span>` : ''}
                        ${rejectedCount > 0 ? `<span class="stat-badge stat-removed">${rejectedCount} 已拒绝</span>` : ''}
                        ${upToDateCount > 0 ? `<span class="stat-badge stat-info">${upToDateCount} 已是最新</span>` : ''}
                    </div>
                    ${hasPendingRevisions ? `
                        <div class="module-actions" onclick="event.stopPropagation()">
                            <button class="action-btn accept-btn" onclick="acceptModuleRevisions('${moduleName}')" title="接受模块所有修订">
                                ✓ 全部接受
                            </button>
                            <button class="action-btn reject-btn" onclick="rejectModuleRevisions('${moduleName}')" title="拒绝模块所有修订">
                                ✗ 全部拒绝
                            </button>
                        </div>
                    ` : ''}
                </div>
            </div>
            <div class="module-content">
    `;

    for (const revision of revisions) {
        html += renderRevisionItem(revision);
    }

    html += `
            </div>
        </div>
    `;

    return html;
}

// 渲染修订项
function renderRevisionItem(revision) {
    const status = storage.getRevisionStatus(revision.path) || 'pending';
    const statusClass = status === 'accepted' ? 'accepted' : status === 'rejected' ? 'rejected' : '';
    const isUpToDate = revision.isUpToDate;

    // 提取字段名（处理嵌套路径）- 使用revision.key而不是再次解析
    const fieldName = revision.key;

    let revisionContent = '';
    if (revision.type === 'added') {
        revisionContent = `
            <span class="revision-type">新增</span>
            <span class="revision-value">
                <span style="color: var(--added);">${formatValue(revision.suggestion)}</span>
                ${isUpToDate ? '<span class="up-to-date-badge">已是最新</span>' : ''}
            </span>
        `;
    } else if (revision.type === 'removed') {
        revisionContent = `
            <span class="revision-type">删除</span>
            <span class="revision-value">
                <span style="color: var(--removed); text-decoration: line-through;">${formatValue(revision.currentValue || revision.originalValue)}</span>
            </span>
        `;
    } else if (revision.type === 'modified') {
        // 显示版本3当前值 → 版本2建议值
        const showCurrentValue = revision.currentValue !== undefined &&
            JSON.stringify(revision.currentValue) !== JSON.stringify(revision.suggestion);

        if (isUpToDate) {
            revisionContent = `
                <span class="revision-type">已更新</span>
                <span class="revision-value">
                    <span style="color: var(--info);">${formatValue(revision.suggestion)}</span>
                    <span class="up-to-date-badge">已是最新</span>
                </span>
            `;
        } else {
            revisionContent = `
                <span class="revision-type">修改</span>
                <span class="revision-value">
                    ${showCurrentValue ?
                `<span style="color: var(--removed); text-decoration: line-through;">${formatValue(revision.currentValue)}</span>` :
                `<span style="color: #6b7280; opacity: 0.7;">${formatValue(revision.originalValue)}</span>`
            }
                    <span class="revision-arrow">→</span>
                    <span style="color: var(--added);">${formatValue(revision.suggestion)}</span>
                </span>
            `;
        }
    }

    // 总是显示操作按钮，允许撤销
    const showAcceptRejectButtons = status === 'pending' && !isUpToDate;
    const showUndoButton = status !== 'pending' || isUpToDate;

    return `
        <div class="field-row">
            <div class="field-label">${fieldName}</div>
            <div class="field-value-container">
                <div class="revision-suggestion ${statusClass} ${isUpToDate ? 'up-to-date' : ''}" data-path="${revision.path}">
                    <div class="revision-content">
                        ${revisionContent}
                    </div>
                    <div class="revision-actions">
                        ${showAcceptRejectButtons ? `
                            <button class="action-btn revision-btn accept-btn" onclick="acceptRevision('${revision.path}')" title="接受修订">
                                ✓
                            </button>
                            <button class="action-btn revision-btn reject-btn" onclick="rejectRevision('${revision.path}')" title="拒绝修订">
                                ✗
                            </button>
                        ` : ''}
                        ${showUndoButton && !isUpToDate ? `
                            <button class="action-btn undo-btn" onclick="undoRevision('${revision.path}')" title="撤销操作">
                                ↺ 撤销
                            </button>
                        ` : ''}
                    </div>
                </div>
                ${revision.currentValue !== undefined && JSON.stringify(revision.currentValue) !== JSON.stringify(revision.originalValue) ? `
                    <div style="margin-top: 4px; font-size: 12px; color: #6b7280;">
                        <strong>版本对比：</strong>
                        版本1: ${formatValue(revision.originalValue)} |
                        版本2: ${formatValue(revision.suggestion)} |
                        版本3: ${formatValue(revision.currentValue)}
                    </div>
                ` : ''}
            </div>
        </div>
    `;
}

// 接受修订
function acceptRevision(path) {
    const statusChange = storage.setRevisionStatus(path, 'accepted');

    // 记录操作历史
    storage.addHistory({
        type: 'accept_revision',
        mode: 'revision',
        path: path,
        detail: `接受修订: ${path}`
    });

    // 立即更新UI
    const element = document.querySelector(`[data-path="${path}"]`);
    if (element) {
        element.classList.remove('rejected');
        element.classList.add('accepted');

        // 更新按钮为撤销按钮
        const actionsContainer = element.querySelector('.revision-actions');
        if (actionsContainer) {
            actionsContainer.innerHTML = `
                <button class="action-btn undo-btn" onclick="undoRevision('${path}')" title="撤销操作">
                    ↺ 撤销
                </button>
            `;
        }
    }

    // 立即更新统计
    updateRevisionStats();

    // 获取模块名并更新模块标题
    const moduleElement = element?.closest('.module-section');
    if (moduleElement) {
        const moduleName = moduleElement.dataset.module;
        updateModuleHeaderInRealtime(moduleName);
    }

    showToast('已接受修订', 'success');
}

// 拒绝修订
function rejectRevision(path) {
    const statusChange = storage.setRevisionStatus(path, 'rejected');

    // 记录操作历史
    storage.addHistory({
        type: 'reject_revision',
        mode: 'revision',
        path: path,
        detail: `拒绝修订: ${path}`
    });

    // 立即更新UI
    const element = document.querySelector(`[data-path="${path}"]`);
    if (element) {
        element.classList.remove('accepted');
        element.classList.add('rejected');

        // 更新按钮为撤销按钮
        const actionsContainer = element.querySelector('.revision-actions');
        if (actionsContainer) {
            actionsContainer.innerHTML = `
                <button class="action-btn undo-btn" onclick="undoRevision('${path}')" title="撤销操作">
                    ↺ 撤销
                </button>
            `;
        }
    }

    // 立即更新统计
    updateRevisionStats();

    // 获取模块名并更新模块标题
    const moduleElement = element?.closest('.module-section');
    if (moduleElement) {
        const moduleName = moduleElement.dataset.module;
        updateModuleHeaderInRealtime(moduleName);
    }

    showToast('已拒绝修订', 'warning');
}

// 撤销修订操作
function undoRevision(path) {
    const statusChange = storage.setRevisionStatus(path, 'pending');

    // 记录操作历史
    storage.addHistory({
        type: 'undo_revision',
        mode: 'revision',
        path: path,
        detail: `撤销修订操作: ${path}`
    });

    // 立即更新UI
    const element = document.querySelector(`[data-path="${path}"]`);
    if (element) {
        const isUpToDate = element.classList.contains('up-to-date');
        element.classList.remove('accepted', 'rejected');

        // 恢复操作按钮
        const actionsContainer = element.querySelector('.revision-actions');
        if (actionsContainer && !isUpToDate) {
            actionsContainer.innerHTML = `
                <button class="action-btn revision-btn accept-btn" onclick="acceptRevision('${path}')" title="接受修订">
                    ✓
                </button>
                <button class="action-btn revision-btn reject-btn" onclick="rejectRevision('${path}')" title="拒绝修订">
                    ✗
                </button>
            `;
        }
    }

    // 立即更新统计
    updateRevisionStats();

    // 获取模块名并更新模块标题
    const moduleElement = element?.closest('.module-section');
    if (moduleElement) {
        const moduleName = moduleElement.dataset.module;
        updateModuleHeaderInRealtime(moduleName);
    }

    showToast('已撤销操作', 'info');
}

// 实时更新模块标题栏
function updateModuleHeaderInRealtime(moduleName) {
    const moduleElement = document.querySelector(`[data-module="${moduleName}"]`);
    if (!moduleElement) return;

    const revisions = moduleElement.querySelectorAll('.revision-suggestion[data-path]');
    let pendingCount = 0, acceptedCount = 0, rejectedCount = 0, upToDateCount = 0;

    revisions.forEach(element => {
        const path = element.dataset.path;
        const status = storage.getRevisionStatus(path) || 'pending';
        const isUpToDate = element.classList.contains('up-to-date');

        if (isUpToDate) {
            upToDateCount++;
        } else {
            switch(status) {
                case 'pending': pendingCount++; break;
                case 'accepted': acceptedCount++; break;
                case 'rejected': rejectedCount++; break;
            }
        }
    });

    const header = moduleElement.querySelector('.module-header');
    const statsContainer = header.querySelector('.module-stats');

    // 更新统计信息
    statsContainer.innerHTML = `
        ${pendingCount > 0 ? `<span class="stat-badge stat-revision">${pendingCount} 待处理</span>` : ''}
        ${acceptedCount > 0 ? `<span class="stat-badge stat-added">${acceptedCount} 已接受</span>` : ''}
        ${rejectedCount > 0 ? `<span class="stat-badge stat-removed">${rejectedCount} 已拒绝</span>` : ''}
        ${upToDateCount > 0 ? `<span class="stat-badge stat-info">${upToDateCount} 已是最新</span>` : ''}
    `;

    // 更新或移除模块操作按钮
    const actionsContainer = header.querySelector('.module-actions');
    if (pendingCount === 0 && actionsContainer) {
        actionsContainer.remove();
    }
}

// 接受模块的所有修订
function acceptModuleRevisions(moduleName) {
    const moduleElement = document.querySelector(`[data-module="${moduleName}"]`);
    if (!moduleElement) return;

    const revisions = moduleElement.querySelectorAll('.revision-suggestion[data-path]');
    let acceptedCount = 0;

    revisions.forEach(element => {
        const path = element.dataset.path;
        const currentStatus = storage.getRevisionStatus(path) || 'pending';
        const isUpToDate = element.classList.contains('up-to-date');

        if (currentStatus === 'pending' && !isUpToDate) {
            acceptRevision(path);
            acceptedCount++;
        }
    });

    // 记录操作历史
    storage.addHistory({
        type: 'accept_module',
        mode: 'revision',
        module: moduleName,
        count: acceptedCount,
        detail: `接受模块"${moduleName}"的${acceptedCount}个修订`
    });

    showToast(`已接受模块"${moduleName}"的 ${acceptedCount} 个修订`, 'success');
}

// 拒绝模块的所有修订
function rejectModuleRevisions(moduleName) {
    const moduleElement = document.querySelector(`[data-module="${moduleName}"]`);
    if (!moduleElement) return;

    const revisions = moduleElement.querySelectorAll('.revision-suggestion[data-path]');
    let rejectedCount = 0;

    revisions.forEach(element => {
        const path = element.dataset.path;
        const currentStatus = storage.getRevisionStatus(path) || 'pending';
        const isUpToDate = element.classList.contains('up-to-date');

        if (currentStatus === 'pending' && !isUpToDate) {
            rejectRevision(path);
            rejectedCount++;
        }
    });

    // 记录操作历史
    storage.addHistory({
        type: 'reject_module',
        mode: 'revision',
        module: moduleName,
        count: rejectedCount,
        detail: `拒绝模块"${moduleName}"的${rejectedCount}个修订`
    });

    showToast(`已拒绝模块"${moduleName}"的 ${rejectedCount} 个修订`, 'warning');
}

// 接受所有修订
function acceptAllRevisions() {
    const revisions = document.querySelectorAll('.revision-suggestion[data-path]');
    let count = 0;

    revisions.forEach(element => {
        const path = element.dataset.path;
        const currentStatus = storage.getRevisionStatus(path) || 'pending';
        const isUpToDate = element.classList.contains('up-to-date');

        if (currentStatus === 'pending' && !isUpToDate) {
            acceptRevision(path);
            count++;
        }
    });

    // 记录操作历史
    storage.addHistory({
        type: 'accept_all',
        mode: 'revision',
        count: count,
        detail: `接受所有修订（${count}个）`
    });

    showToast(`已接受所有修订（${count}个）`, 'success');
}

// 拒绝所有修订
function rejectAllRevisions() {
    const revisions = document.querySelectorAll('.revision-suggestion[data-path]');
    let count = 0;

    revisions.forEach(element => {
        const path = element.dataset.path;
        const currentStatus = storage.getRevisionStatus(path) || 'pending';
        const isUpToDate = element.classList.contains('up-to-date');

        if (currentStatus === 'pending' && !isUpToDate) {
            rejectRevision(path);
            count++;
        }
    });

    // 记录操作历史
    storage.addHistory({
        type: 'reject_all',
        mode: 'revision',
        count: count,
        detail: `拒绝所有修订（${count}个）`
    });

    showToast(`已拒绝所有修订（${count}个）`, 'warning');
}

// 更新修订统计（实时）
function updateRevisionStats() {
    let pending = 0, accepted = 0, rejected = 0, upToDate = 0;

    const allRevisions = document.querySelectorAll('.revision-suggestion[data-path]');

    allRevisions.forEach(element => {
        const path = element.dataset.path;
        const status = storage.getRevisionStatus(path) || 'pending';
        const isUpToDate = element.classList.contains('up-to-date');

        if (isUpToDate) {
            upToDate++;
        } else {
            switch(status) {
                case 'pending':
                    pending++;
                    break;
                case 'accepted':
                    accepted++;
                    break;
                case 'rejected':
                    rejected++;
                    break;
            }
        }
    });

    document.getElementById('pendingRevisions').textContent = pending;
    document.getElementById('acceptedRevisions').textContent = accepted;
    document.getElementById('rejectedRevisions').textContent = rejected;
    document.getElementById('upToDateRevisions').textContent = upToDate;
}

// 导出结果（增强调试信息）
function exportResult() {
    if (!window.targetData) {
        showToast('没有可导出的数据', 'warning');
        return;
    }

    console.log('=== 导出开始 ===');
    console.log('原始版本3数据:', JSON.stringify(window.targetData, null, 2));
    console.log('修订建议总数:', window.revisionSuggestions.size);

    // 显示所有修订建议
    console.log('所有修订建议:');
    window.revisionSuggestions.forEach((value, key) => {
        console.log(`- ${key}:`, value);
    });

    // 显示接受的修订
    const acceptedRevisions = Array.from(storage.getAllRevisions())
        .filter(([_, status]) => status === 'accepted');
    console.log('接受的修订:', acceptedRevisions);

    // 应用所有接受的修订到版本3
    const exportData = applyAcceptedRevisions(window.targetData);

    console.log('=== 导出完成 ===');
    console.log('最终导出数据:', JSON.stringify(exportData, null, 2));

    // 下载JSON文件
    const dataStr = JSON.stringify(exportData, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);

    const exportFileDefaultName = `revised_${new Date().getTime()}.json`;

    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();

    // 记录操作历史
    storage.addHistory({
        type: 'export',
        detail: '导出修订后的JSON文件'
    });

    showToast('导出成功，请查看控制台了解详情', 'success');
}

// 应用接受的修订（增强版，处理所有嵌套情况）
function applyAcceptedRevisions(data) {
    // 深拷贝数据
    const result = deepClone(data);

    // 获取所有接受的修订
    const revisions = storage.getAllRevisions();

    console.log('=== 开始应用修订 ===');
    console.log('原始数据结构:', Object.keys(result));
    console.log('总修订数:', revisions.size);

    // 按照路径长度排序，先处理父级路径
    const sortedRevisions = Array.from(revisions.entries())
        .filter(([_, status]) => status === 'accepted')
        .sort(([a], [b]) => a.split('.').length - b.split('.').length);

    console.log('接受的修订数量:', sortedRevisions.length);

    for (const [path, status] of sortedRevisions) {
        // 获取修订建议的详细信息
        const revisionInfo = window.revisionSuggestions.get(path);
        if (!revisionInfo) {
            console.warn('找不到修订信息:', path);
            continue;
        }

        console.log(`\n处理修订: ${path}`);
        console.log('修订信息:', revisionInfo);

        try {
            // 特别处理：如果是嵌套字段的修订
            if (revisionInfo.fullPath && !revisionInfo.fullPath.includes('.') && !revisionInfo.fullPath.includes('[')) {
                // 这是一个简单字段，但可能在嵌套对象中
                applySimpleFieldRevision(result, revisionInfo);
            } else if (revisionInfo.isArray && revisionInfo.type !== 'removed') {
                // 如果是数组项
                applyArrayRevision(result, revisionInfo);
            } else {
                // 普通字段修订
                applyRevisionToData(result, revisionInfo);
            }

            console.log('修订应用成功');
        } catch (error) {
            console.error('应用修订失败:', path, error);
        }
    }

    console.log('=== 修订应用完成 ===');
    return result;
}

// 应用简单字段的修订（可能在嵌套对象中）
function applySimpleFieldRevision(data, revisionInfo) {
    const { moduleName, fullPath, suggestion, type } = revisionInfo;

    console.log('应用简单字段修订:', { moduleName, fullPath, suggestion });

    // 确保模块存在
    if (!data[moduleName]) {
        if (type === 'added') {
            data[moduleName] = {};
        } else {
            console.warn('模块不存在:', moduleName);
            return;
        }
    }

    // 查找字段并更新
    function findAndUpdate(obj, fieldName, newValue) {
        for (const key in obj) {
            if (key === fieldName) {
                console.log(`找到字段 ${fieldName}，更新值: ${obj[key]} → ${newValue}`);
                obj[key] = newValue;
                return true;
            } else if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
                // 递归查找
                if (findAndUpdate(obj[key], fieldName, newValue)) {
                    return true;
                }
            }
        }
        return false;
    }

    // 在模块中查找并更新字段
    const updated = findAndUpdate(data[moduleName], fullPath, suggestion);
    if (!updated) {
        console.warn(`未找到字段: ${fullPath} in ${moduleName}`);
        // 如果没找到，尝试直接设置
        data[moduleName][fullPath] = suggestion;
    }
}

// 应用数组修订
function applyArrayRevision(data, revisionInfo) {
    const { moduleName, fullPath, suggestion, type, arrayKey, arrayIndex } = revisionInfo;

    console.log('应用数组修订:', { moduleName, fullPath, type });

    // 确保模块存在
    if (!data[moduleName]) {
        data[moduleName] = {};
    }

    // 解析数组路径
    const match = fullPath.match(/^(.*?)\[(.*?)\]$/);
    if (!match) {
        console.error('无效的数组路径:', fullPath);
        return;
    }

    const arrayPath = match[1];
    const key = match[2];

    // 导航到数组
    let current = data[moduleName];
    if (arrayPath) {
        const parts = arrayPath.split('.');
        for (const part of parts) {
            if (!current[part]) {
                current[part] = [];
            }
            current = current[part];
        }
    }

    // 确保是数组
    if (!Array.isArray(current)) {
        console.error('期望是数组，但不是:', current);
        return;
    }

    if (type === 'added') {
        // 新增数组项
        current.push(suggestion);
    } else if (type === 'modified') {
        if (arrayIndex !== undefined) {
            // 基于索引的修改
            current[arrayIndex] = suggestion;
        } else if (arrayKey) {
            // 基于键的修改（需要查找）
            const indexFields = getIndexFieldsForModule(moduleName);
            const index = current.findIndex(item => {
                const itemKey = indexFields.map(f => item[f]).filter(v => v).join('|');
                return itemKey === key;
            });

            if (index >= 0) {
                current[index] = suggestion;
            } else {
                console.warn('找不到要修改的数组项:', key);
            }
        }
    }

    console.log('数组修订完成');
}

// 应用单个修订到数据（优化版，处理整个对象的修订）
function applyRevisionToData(data, revisionInfo) {
    const { type, suggestion, fullPath, moduleName } = revisionInfo;

    console.log('应用修订详情:', { type, suggestion, fullPath, moduleName });

    // 确保模块存在
    if (!data[moduleName]) {
        if (type === 'added') {
            data[moduleName] = {};
        } else {
            console.warn('模块不存在:', moduleName);
            return;
        }
    }

    // 处理路径
    let current = data[moduleName];
    const pathParts = fullPath.split('.');

    // 如果只有一个部分，直接在模块下设置
    if (pathParts.length === 1 && !pathParts[0].includes('[')) {
        if (type === 'added' || type === 'modified') {
            // 如果suggestion是对象，直接替换整个对象
            current[pathParts[0]] = suggestion;
            console.log(`设置 ${pathParts[0]} =`, suggestion);
        }
        console.log('简单路径修订完成');
        return;
    }

    // 处理复杂路径
    for (let i = 0; i < pathParts.length - 1; i++) {
        const part = pathParts[i];

        // 处理数组索引
        const arrayMatch = part.match(/^(.*?)\[(\d+)\]$/);
        if (arrayMatch) {
            const arrayName = arrayMatch[1];
            const index = parseInt(arrayMatch[2]);

            // 如果有数组名，先导航到数组
            if (arrayName) {
                if (!current[arrayName]) {
                    current[arrayName] = [];
                }
                current = current[arrayName];
            }

            // 确保数组有足够的长度
            if (!Array.isArray(current)) {
                console.error('期望是数组，但不是:', current);
                return;
            }

            while (current.length <= index) {
                current.push({});
            }
            current = current[index];
        } else {
            // 普通对象属性
            if (!current[part]) {
                // 检查下一个部分是否包含数组索引
                const nextPart = pathParts[i + 1];
                if (nextPart && nextPart.includes('[')) {
                    current[part] = [];
                } else {
                    current[part] = {};
                }
            }
            current = current[part];
        }
    }

    // 应用修订到最后一个键
    const finalKey = pathParts[pathParts.length - 1];

    // 处理最后一个键中的数组索引
    const finalArrayMatch = finalKey.match(/^(.*?)\[(\d+)\]$/);
    if (finalArrayMatch) {
        const arrayName = finalArrayMatch[1];
        const index = parseInt(finalArrayMatch[2]);

        if (arrayName) {
            if (!current[arrayName]) {
                current[arrayName] = [];
            }
            current = current[arrayName];
        }

        if (Array.isArray(current)) {
            while (current.length <= index) {
                current.push(null);
            }
            current[index] = suggestion;
        }
    } else {
        // 普通字段
        if (type === 'added' || type === 'modified') {
            current[finalKey] = suggestion;
            console.log(`设置 ${finalKey} =`, suggestion);
        }
    }

    console.log('修订应用完成');
}