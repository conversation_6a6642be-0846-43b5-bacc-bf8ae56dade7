// UI相关功能

// 显示Toast提示
function showToast(message, type = 'info') {
    const toast = document.getElementById('toast');
    const icon = toast.querySelector('.toast-icon');
    const msg = toast.querySelector('.toast-message');

    toast.className = `toast toast-${type}`;
    icon.textContent = type === 'success' ? '✓' : type === 'error' ? '✗' : type === 'warning' ? '!' : 'ℹ';
    msg.textContent = message;

    toast.classList.add('show');
    setTimeout(() => {
        toast.classList.remove('show');
    }, 3000);
}

// 显示模态框
function showModal(title, content) {
    document.getElementById('modalTitle').textContent = title;
    document.getElementById('modalBody').innerHTML = content;
    document.getElementById('modal').style.display = 'block';
}

// 关闭模态框
function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// 显示操作历史
function showHistory() {
    const history = storage.getHistory();

    if (history.length === 0) {
        showModal('操作历史', `
            <div class="history-empty">
                <div class="empty-state-icon">📜</div>
                <div class="empty-state-text">暂无操作历史</div>
            </div>
        `);
        return;
    }

    const content = `
        <div class="history-container">
            ${history.map(item => `
                <div class="history-item">
                    <div class="history-info">
                        <div class="history-type">
                            <span class="history-type-icon">${getOperationIcon(item.type)}</span>
                            ${getOperationTypeName(item.type)}
                        </div>
                        <div class="history-detail">${item.detail || ''}</div>
                    </div>
                    <div class="history-time">${formatTime(item.timestamp)}</div>
                </div>
            `).join('')}
        </div>
        <div style="margin-top: 20px; text-align: right;">
            <button class="btn btn-secondary" onclick="clearHistory()">清空历史</button>
            <button class="btn btn-primary" onclick="closeModal('modal')">关闭</button>
        </div>
    `;

    showModal('操作历史', content);
}

// 清空历史
function clearHistory() {
    if (confirm('确定要清空所有操作历史吗？')) {
        storage.clearHistory();
        closeModal('modal');
        showToast('操作历史已清空', 'success');
    }
}

// 显示版本历史
function showVersionHistory() {
    const versions = storage.getVersions();

    if (versions.length === 0) {
        showModal('版本历史', `
            <div class="history-empty">
                <div class="empty-state-icon">📋</div>
                <div class="empty-state-text">暂无版本历史</div>
            </div>
        `);
        return;
    }

    const content = `
        <div class="version-list">
            ${versions.map((version, index) => `
                <div class="version-item">
                    <div class="version-info">
                        <div class="version-name">${version.name || `版本 ${version.id}`}</div>
                        <div class="version-time">${formatTime(version.timestamp)}</div>
                    </div>
                    <div class="version-details">
                        版本1: ${version.data.left ? '✓' : '✗'} | 
                        版本2: ${version.data.right ? '✓' : '✗'} | 
                        版本3: ${version.data.target ? '✓' : '✗'}
                    </div>
                    <div class="version-actions">
                        <button class="btn btn-primary" onclick="loadVersion(${index})">
                            <span>📂</span> 加载此版本
                        </button>
                        <button class="btn btn-secondary" onclick="previewVersion(${index})">
                            <span>👁️</span> 预览
                        </button>
                    </div>
                </div>
            `).join('')}
        </div>
    `;

    showModal('版本历史', content);
}

// 加载版本
function loadVersion(index) {
    const versions = storage.getVersions();
    const version = versions[index];
    if (version) {
        window.leftData = version.data.left;
        window.rightData = version.data.right;
        window.targetData = version.data.target || null;

        // 记录操作历史
        storage.addHistory({
            type: 'load_version',
            detail: `加载${version.name || `版本 ${version.id}`}`
        });

        closeModal('modal');
        compareData();
        showToast(`已加载${version.name || `版本 ${version.id}`}`, 'success');
    }
}

// 预览版本
function previewVersion(index) {
    const versions = storage.getVersions();
    const version = versions[index];
    if (!version) return;

    const content = `
        <div style="font-family: monospace; font-size: 13px;">
            <h3 style="margin-bottom: 16px;">${version.name || `版本 ${version.id}`}</h3>
            <div style="color: #6b7280; margin-bottom: 16px;">
                创建时间: ${new Date(version.timestamp).toLocaleString('zh-CN')}
            </div>
            
            ${version.data.left ? `
                <h4 style="margin: 16px 0 8px 0;">版本1数据预览:</h4>
                <pre style="background: #f3f4f6; padding: 12px; border-radius: 8px; overflow: auto; max-height: 200px;">
${JSON.stringify(Object.entries(version.data.left).slice(0, 3).reduce((obj, [k, v]) => ({...obj, [k]: v}), {}), null, 2)}
                </pre>
            ` : ''}
            
            ${version.data.right ? `
                <h4 style="margin: 16px 0 8px 0;">版本2数据预览:</h4>
                <pre style="background: #f3f4f6; padding: 12px; border-radius: 8px; overflow: auto; max-height: 200px;">
${JSON.stringify(Object.entries(version.data.right).slice(0, 3).reduce((obj, [k, v]) => ({...obj, [k]: v}), {}), null, 2)}
                </pre>
            ` : ''}
            
            ${version.data.target ? `
                <h4 style="margin: 16px 0 8px 0;">版本3数据预览:</h4>
                <pre style="background: #f3f4f6; padding: 12px; border-radius: 8px; overflow: auto; max-height: 200px;">
${JSON.stringify(Object.entries(version.data.target).slice(0, 3).reduce((obj, [k, v]) => ({...obj, [k]: v}), {}), null, 2)}
                </pre>
            ` : ''}
            
            <div style="margin-top: 20px; text-align: right;">
                <button class="btn btn-primary" onclick="loadVersion(${index})">加载此版本</button>
                <button class="btn btn-secondary" onclick="showVersionHistory()">返回</button>
            </div>
        </div>
    `;

    showModal(`版本预览 - ${version.name || `版本 ${version.id}`}`, content);
}

// 调试数据
function debugData() {
    const hasVersion1 = window.leftData !== null;
    const hasVersion2 = window.rightData !== null;
    const hasVersion3 = window.targetData !== null;

    const content = `
        <div style="font-family: monospace; font-size: 14px;">
            <h3 style="margin-bottom: 16px;">数据状态</h3>
            <div style="margin-bottom: 12px;">
                <strong>版本1:</strong> ${hasVersion1 ? '✓ 已加载' : '✗ 未加载'}
                ${hasVersion1 ? `<br>模块数: ${Object.keys(window.leftData).length}` : ''}
            </div>
            <div style="margin-bottom: 12px;">
                <strong>版本2:</strong> ${hasVersion2 ? '✓ 已加载' : '✗ 未加载'}
                ${hasVersion2 ? `<br>模块数: ${Object.keys(window.rightData).length}` : ''}
            </div>
            <div style="margin-bottom: 12px;">
                <strong>版本3:</strong> ${hasVersion3 ? '✓ 已加载' : '✗ 未加载'}
                ${hasVersion3 ? `<br>模块数: ${Object.keys(window.targetData).length}` : ''}
            </div>
            <div style="margin-bottom: 12px;">
                <strong>当前模式:</strong> ${storage.get('currentMode') === 'revision' ? '修订模式' : '对比模式'}
            </div>
            <div style="margin-bottom: 12px;">
                <strong>接受的更改数:</strong> ${storage.get('acceptedChanges').size}
            </div>
            <div style="margin-bottom: 12px;">
                <strong>拒绝的更改数:</strong> ${storage.get('rejectedChanges').size}
            </div>
            <div style="margin-bottom: 12px;">
                <strong>修订状态数:</strong> ${storage.getAllRevisions().size}
            </div>

            ${hasVersion1 && hasVersion2 && hasVersion3 ? `
                <h3 style="margin-top: 24px; margin-bottom: 16px;">示例数据预览</h3>
                <div style="margin-bottom: 12px;">
                    <strong>版本1 示例:</strong>
                    <pre style="background: #f3f4f6; padding: 8px; border-radius: 4px; overflow: auto; max-height: 200px;">
${JSON.stringify(Object.entries(window.leftData).slice(0, 2).reduce((obj, [k, v]) => ({...obj, [k]: v}), {}), null, 2)}
                    </pre>
                </div>
                <div style="margin-bottom: 12px;">
                    <strong>版本2 示例:</strong>
                    <pre style="background: #f3f4f6; padding: 8px; border-radius: 4px; overflow: auto; max-height: 200px;">
${JSON.stringify(Object.entries(window.rightData).slice(0, 2).reduce((obj, [k, v]) => ({...obj, [k]: v}), {}), null, 2)}
                    </pre>
                </div>
                <div style="margin-bottom: 12px;">
                    <strong>版本3 示例:</strong>
                    <pre style="background: #f3f4f6; padding: 8px; border-radius: 4px; overflow: auto; max-height: 200px;">
${JSON.stringify(Object.entries(window.targetData).slice(0, 2).reduce((obj, [k, v]) => ({...obj, [k]: v}), {}), null, 2)}
                    </pre>
                </div>
            ` : ''}

            <div style="margin-top: 24px;">
                <button class="btn btn-secondary" onclick="window.debugDataStructure && window.debugDataStructure()" style="margin-right: 10px;">
                    查看数据结构（控制台）
                </button>
                <button class="btn btn-primary" onclick="closeModal('modal')">关闭</button>
            </div>
        </div>
    `;

    showModal('调试信息', content);
}

// 切换模块展开/折叠
function toggleModule(header) {
    const section = header.closest('.module-section');
    section.classList.toggle('collapsed');
}

// 展开所有模块
function expandAllModules() {
    document.querySelectorAll('.module-section').forEach(section => {
        section.classList.remove('collapsed');
    });
}

// 折叠所有模块
function collapseAllModules() {
    document.querySelectorAll('.module-section').forEach(section => {
        section.classList.add('collapsed');
    });
}

// 切换配置面板折叠状态
function toggleConfigPanel() {
    const panel = document.querySelector('.config-panel');
    panel.classList.toggle('collapsed');
}

// 切换模块配置折叠状态
function toggleModuleConfig(header) {
    const moduleConfig = header.closest('.module-config');
    moduleConfig.classList.toggle('collapsed');
}

// 渲染模块配置
function renderModuleConfigs() {
    const container = document.getElementById('moduleConfigs');
    const config = storage.getModuleIndexConfig();

    let html = '';
    for (const [module, fields] of Object.entries(config)) {
        if (typeof fields === 'object' && !Array.isArray(fields)) {
            // 嵌套模块
            for (const [subModule, subFields] of Object.entries(fields)) {
                html += renderModuleConfigItem(`${module} - ${subModule}`, subFields);
            }
        } else {
            html += renderModuleConfigItem(module, fields);
        }
    }

    container.innerHTML = html;
}

// 渲染单个模块配置项
function renderModuleConfigItem(moduleName, fields) {
    const fieldsHtml = fields.map(field => `
        <span class="tag">
            ${field}
            <span class="tag-remove" onclick="removeModuleIndexField('${moduleName}', '${field}')">&times;</span>
        </span>
    `).join('');

    return `
        <div class="module-config" data-module="${moduleName}">
            <div class="module-config-header" onclick="toggleModuleConfig(this)">
                <span>${moduleName}</span>
                <span class="module-config-arrow">▼</span>
            </div>
            <div class="module-config-body">
                <div class="tag-input">
                    ${fieldsHtml}
                    <input type="text"
                           placeholder="添加索引字段"
                           onkeypress="handleModuleIndexFieldInput(event, '${moduleName}')"
                           style="border: none; outline: none; flex: 1; min-width: 150px;">
                </div>
            </div>
        </div>
    `;
}

// 处理模块索引字段输入
function handleModuleIndexFieldInput(event, moduleName) {
    if (event.key === 'Enter') {
        const input = event.target;
        const value = input.value.trim();
        if (value) {
            const config = storage.getModuleIndexConfig();

            // 处理嵌套模块
            if (moduleName.includes(' - ')) {
                const [parent, sub] = moduleName.split(' - ');
                if (!config[parent][sub].includes(value)) {
                    config[parent][sub].push(value);
                }
            } else {
                if (!config[moduleName].includes(value)) {
                    config[moduleName].push(value);
                }
            }

            storage.setModuleIndexConfig(config);
            renderModuleConfigs();

            // 如果已有数据，重新对比
            if (window.leftData && window.rightData) {
                compareData();
            }
        }
        input.value = '';
    }
}

// 移除模块索引字段
function removeModuleIndexField(moduleName, field) {
    const config = storage.getModuleIndexConfig();

    if (moduleName.includes(' - ')) {
        const [parent, sub] = moduleName.split(' - ');
        const index = config[parent][sub].indexOf(field);
        if (index > -1) {
            config[parent][sub].splice(index, 1);
        }
    } else {
        const index = config[moduleName].indexOf(field);
        if (index > -1) {
            config[moduleName].splice(index, 1);
        }
    }

    storage.setModuleIndexConfig(config);
    renderModuleConfigs();

    if (window.leftData && window.rightData) {
        compareData();
    }
}

// 设置过滤器
function setFilter(filter) {
    storage.set('currentFilter', filter);

    // 更新按钮状态
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.toggle('active', btn.dataset.filter === filter);
    });

    // 重新渲染内容
    applyFilter();
}

// 设置修订过滤器
function setRevisionFilter(filter) {
    storage.set('revisionFilter', filter);

    // 更新按钮状态
    document.querySelectorAll('.revision-filter-btn').forEach(btn => {
        btn.classList.toggle('active', btn.dataset.filter === filter);
    });

    // 应用过滤器
    applyRevisionFilter();
}

// 应用过滤器
function applyFilter() {
    const filter = storage.get('currentFilter');
    const fieldRows = document.querySelectorAll('.field-row');

    fieldRows.forEach(row => {
        const hasAdded = row.querySelector('.revision-added');
        const hasRemoved = row.querySelector('.revision-removed');
        const hasModified = row.querySelector('.revision-modified');

        let shouldShow = false;

        switch(filter) {
            case 'all':
                shouldShow = true;
                break;
            case 'added':
                shouldShow = hasAdded;
                break;
            case 'removed':
                shouldShow = hasRemoved;
                break;
            case 'modified':
                shouldShow = hasModified;
                break;
        }

        row.style.display = shouldShow ? 'flex' : 'none';
    });

    // 更新模块的显示状态
    updateModuleVisibility();
}

// 应用修订过滤器
function applyRevisionFilter() {
    const filter = storage.get('revisionFilter');
    const revisions = document.querySelectorAll('.revision-suggestion');

    revisions.forEach(revision => {
        const path = revision.dataset.path;
        const status = storage.getRevisionStatus(path) || 'pending';
        const isUpToDate = revision.classList.contains('up-to-date');

        let shouldShow = false;
        switch(filter) {
            case 'all':
                shouldShow = true;
                break;
            case 'pending':
                shouldShow = status === 'pending' && !isUpToDate;
                break;
            case 'accepted':
                shouldShow = status === 'accepted';
                break;
            case 'rejected':
                shouldShow = status === 'rejected';
                break;
            case 'uptodate':
                shouldShow = isUpToDate;
                break;
        }

        revision.style.display = shouldShow ? 'flex' : 'none';
    });

    // 更新模块可见性
    updateModuleVisibility();
}

// 更新模块可见性
function updateModuleVisibility() {
    const modules = document.querySelectorAll('.module-section');

    modules.forEach(module => {
        const content = module.querySelector('.module-content');
        const visibleRows = content.querySelectorAll('.field-row[style="display: flex;"], .field-row:not([style*="display"]), .revision-suggestion[style="display: flex;"], .revision-suggestion:not([style*="display"])');

        // 如果没有可见的行，隐藏整个模块
        module.style.display = visibleRows.length > 0 ? 'block' : 'none';
    });
}