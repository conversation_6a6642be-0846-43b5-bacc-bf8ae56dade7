// multi-record-ui.js - 多记录对比UI模块

// 渲染多记录模块的表格视图
function renderMultiRecordModule(moduleName, data1, data2, moduleStats) {
    const records1 = Array.isArray(data1) ? data1 : [];
    const records2 = Array.isArray(data2) ? data2 : [];

    // 获取索引字段
    const indexFields = getIndexFieldsForModule(moduleName);

    // 生成记录的显示标识
    function getRecordIdentifier(record) {
        if (indexFields.length > 0) {
            return indexFields.map(field => record[field] || '').filter(v => v).join(' - ');
        }
        // 如果没有配置索引字段，尝试使用常见字段
        return record['不良事件名称'] || record['疾病名称'] || record['药品名称'] || '未命名记录';
    }

    // 分析记录匹配情况
    const { matched, unmatched1, unmatched2 } = matchRecords(records1, records2, indexFields);

    let html = `
        <div class="module-section multi-record-module" data-module="${moduleName}">
            <div class="module-header has-changes" onclick="toggleModule(this)">
                <div class="module-title">
                    <span class="module-arrow">▼</span>
                    ${moduleName} (多记录)
                </div>
                <div class="module-stats">
                    <span class="stat-badge stat-info">${records1.length} → ${records2.length} 条记录</span>
                    ${moduleStats.added > 0 ? `<span class="stat-badge stat-added">+${moduleStats.added}</span>` : ''}
                    ${moduleStats.removed > 0 ? `<span class="stat-badge stat-removed">-${moduleStats.removed}</span>` : ''}
                    ${moduleStats.modified > 0 ? `<span class="stat-badge stat-modified">~${moduleStats.modified}</span>` : ''}
                </div>
            </div>
            <div class="module-content">
                <div class="multi-record-toolbar">
                    <button class="btn btn-secondary" onclick="toggleRecordView('${moduleName}', 'table')">
                        <span>📊</span> 表格视图
                    </button>
                    <button class="btn btn-secondary" onclick="toggleRecordView('${moduleName}', 'detail')">
                        <span>📝</span> 详细视图
                    </button>
                    ${records2.length > 1 ? `
                        <button class="btn btn-primary" onclick="showMergeDialog('${moduleName}')">
                            <span>🔄</span> 合并记录
                        </button>
                    ` : ''}
                </div>
                
                <div class="record-view-container" id="recordView_${moduleName}">
                    ${renderTableView(moduleName, records1, records2, matched, unmatched1, unmatched2)}
                </div>
            </div>
        </div>
    `;

    return html;
}

// 渲染表格视图
function renderTableView(moduleName, records1, records2, matched, unmatched1, unmatched2) {
    // 获取所有可能的字段
    const allFields = new Set();
    [...records1, ...records2].forEach(record => {
        Object.keys(record).forEach(key => {
            if (key !== '报告分类' && key !== '报告模块') {
                allFields.add(key);
            }
        });
    });

    const fields = Array.from(allFields);
    const indexFields = getIndexFieldsForModule(moduleName);

    let html = `
        <div class="multi-record-table-container">
            <table class="multi-record-table">
                <thead>
                    <tr>
                        <th class="record-version">版本</th>
                        <th class="record-status">状态</th>
                        ${fields.map(field => `
                            <th class="${indexFields.includes(field) ? 'index-field' : ''}">
                                ${field}
                                ${indexFields.includes(field) ? '<span class="index-indicator">🔑</span>' : ''}
                            </th>
                        `).join('')}
                        <th class="record-actions">操作</th>
                    </tr>
                </thead>
                <tbody>
    `;

    // 渲染匹配的记录对
    matched.forEach(({ record1, record2, index1, index2 }) => {
        const changes = compareRecords(record1, record2, fields);

        // 版本1记录
        html += `
            <tr class="record-row version1" data-index="${index1}">
                <td class="record-version"><span class="version-badge">版本1</span></td>
                <td class="record-status">${changes.hasChanges ? '<span class="status-modified">已修改</span>' : '<span class="status-unchanged">无变化</span>'}</td>
                ${fields.map(field => {
            const hasChange = changes.fields[field];
            return `<td class="${hasChange ? 'field-changed' : ''}">${formatValue(record1[field])}</td>`;
        }).join('')}
                <td class="record-actions">
                    ${changes.hasChanges ? `
                        <button class="action-btn view-btn" onclick="viewRecordDiff('${moduleName}', ${index1}, ${index2})">
                            查看差异
                        </button>
                    ` : ''}
                </td>
            </tr>
        `;

        // 版本2记录
        html += `
            <tr class="record-row version2" data-index="${index2}">
                <td class="record-version"><span class="version-badge version2">版本2</span></td>
                <td class="record-status">${changes.hasChanges ? '<span class="status-modified">已修改</span>' : '<span class="status-unchanged">无变化</span>'}</td>
                ${fields.map(field => {
            const hasChange = changes.fields[field];
            const oldValue = record1[field];
            const newValue = record2[field];

            if (hasChange) {
                return `<td class="field-changed">
                            ${oldValue !== undefined && newValue !== undefined ?
                    `<span class="old-value">${formatValue(oldValue)}</span> → <span class="new-value">${formatValue(newValue)}</span>` :
                    `<span class="new-value">${formatValue(newValue)}</span>`
                }
                        </td>`;
            } else {
                return `<td>${formatValue(record2[field])}</td>`;
            }
        }).join('')}
                <td class="record-actions">
                    <label class="merge-checkbox">
                        <input type="checkbox" name="merge_${moduleName}" value="${index2}" />
                        选择合并
                    </label>
                </td>
            </tr>
            <tr class="record-separator"><td colspan="${fields.length + 3}"></td></tr>
        `;
    });

    // 渲染版本1独有的记录（已删除）
    unmatched1.forEach(({ record, index }) => {
        html += `
            <tr class="record-row removed" data-index="${index}">
                <td class="record-version"><span class="version-badge">版本1</span></td>
                <td class="record-status"><span class="status-removed">已删除</span></td>
                ${fields.map(field => `<td class="field-removed">${formatValue(record[field])}</td>`).join('')}
                <td class="record-actions">
                    <button class="action-btn reject-btn" onclick="rejectRecordRemoval('${moduleName}', ${index})">
                        保留记录
                    </button>
                </td>
            </tr>
        `;
    });

    // 渲染版本2独有的记录（新增）
    unmatched2.forEach(({ record, index }) => {
        html += `
            <tr class="record-row added" data-index="${index}">
                <td class="record-version"><span class="version-badge version2">版本2</span></td>
                <td class="record-status"><span class="status-added">新增</span></td>
                ${fields.map(field => `<td class="field-added">${formatValue(record[field])}</td>`).join('')}
                <td class="record-actions">
                    <button class="action-btn accept-btn" onclick="acceptNewRecord('${moduleName}', ${index})">
                        接受新增
                    </button>
                    <label class="merge-checkbox">
                        <input type="checkbox" name="merge_${moduleName}" value="${index}" />
                        选择合并
                    </label>
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    return html;
}

// 匹配记录
function matchRecords(records1, records2, indexFields) {
    const matched = [];
    const used2 = new Set();
    const unmatched1 = [];
    const unmatched2 = [];

    // 基于索引字段匹配
    records1.forEach((record1, index1) => {
        let matchFound = false;

        if (indexFields.length > 0) {
            const key1 = indexFields.map(field => record1[field]).filter(v => v).join('|');

            records2.forEach((record2, index2) => {
                if (!used2.has(index2)) {
                    const key2 = indexFields.map(field => record2[field]).filter(v => v).join('|');
                    if (key1 && key1 === key2) {
                        matched.push({ record1, record2, index1, index2 });
                        used2.add(index2);
                        matchFound = true;
                        return;
                    }
                }
            });
        }

        if (!matchFound) {
            unmatched1.push({ record: record1, index: index1 });
        }
    });

    // 收集未匹配的版本2记录
    records2.forEach((record, index) => {
        if (!used2.has(index)) {
            unmatched2.push({ record, index });
        }
    });

    return { matched, unmatched1, unmatched2 };
}

// 比较两条记录
function compareRecords(record1, record2, fields) {
    const changes = { hasChanges: false, fields: {} };

    fields.forEach(field => {
        const value1 = record1[field];
        const value2 = record2[field];

        if (JSON.stringify(value1) !== JSON.stringify(value2)) {
            changes.hasChanges = true;
            changes.fields[field] = true;
        }
    });

    return changes;
}

// 显示合并对话框
function showMergeDialog(moduleName) {
    const selectedIndexes = [];
    document.querySelectorAll(`input[name="merge_${moduleName}"]:checked`).forEach(checkbox => {
        selectedIndexes.push(parseInt(checkbox.value));
    });

    if (selectedIndexes.length < 2) {
        showToast('请至少选择2条记录进行合并', 'warning');
        return;
    }

    // 获取选中的记录
    const records = selectedIndexes.map(index => {
        const moduleData = window.rightData[moduleName];
        return Array.isArray(moduleData) ? moduleData[index] : null;
    }).filter(r => r);

    // 生成合并预览
    const mergedRecord = mergeRecords(records);

    const content = `
        <div class="merge-dialog">
            <h3>合并记录预览</h3>
            <p>将合并 ${records.length} 条记录为 1 条</p>
            
            <div class="merge-preview">
                <h4>合并后的记录：</h4>
                <table class="merge-preview-table">
                    ${Object.entries(mergedRecord).map(([key, value]) => {
        if (key === '报告分类' || key === '报告模块') return '';

        // 检查是否有冲突
        const hasConflict = records.some(r => r[key] !== value && r[key] !== undefined);

        return `
                            <tr class="${hasConflict ? 'has-conflict' : ''}">
                                <td class="field-name">${key}</td>
                                <td class="field-value">
                                    ${formatValue(value)}
                                    ${hasConflict ? '<span class="conflict-indicator">⚠️ 有冲突</span>' : ''}
                                </td>
                            </tr>
                        `;
    }).join('')}
                </table>
            </div>
            
            <div class="merge-actions">
                <button class="btn btn-secondary" onclick="closeModal('modal')">取消</button>
                <button class="btn btn-primary" onclick="confirmMerge('${moduleName}', ${JSON.stringify(selectedIndexes)})">
                    确认合并
                </button>
            </div>
        </div>
    `;

    showModal('合并记录', content);
}

// 合并记录
function mergeRecords(records) {
    const merged = {};

    // 合并策略：
    // 1. 对于相同的字段，取最新的值（最后一条记录的值）
    // 2. 对于数组字段，合并去重
    // 3. 对于日期字段，取最新的日期

    records.forEach(record => {
        Object.entries(record).forEach(([key, value]) => {
            if (value === undefined || value === null || value === '') return;

            if (Array.isArray(value)) {
                // 数组合并
                if (!merged[key]) merged[key] = [];
                merged[key] = [...new Set([...merged[key], ...value])];
            } else if (key.includes('日期') && merged[key]) {
                // 日期字段，保留较新的
                if (new Date(value) > new Date(merged[key])) {
                    merged[key] = value;
                }
            } else {
                // 其他字段，后面的覆盖前面的
                merged[key] = value;
            }
        });
    });

    return merged;
}

// 确认合并
function confirmMerge(moduleName, selectedIndexes) {
    // 获取模块数据
    const moduleData = window.rightData[moduleName];
    if (!Array.isArray(moduleData)) return;

    // 获取要合并的记录
    const recordsToMerge = selectedIndexes.map(index => moduleData[index]).filter(r => r);

    // 合并记录
    const mergedRecord = mergeRecords(recordsToMerge);

    // 创建新的数组，移除被合并的记录，添加合并后的记录
    const newModuleData = moduleData.filter((_, index) => !selectedIndexes.includes(index));
    newModuleData.push(mergedRecord);

    // 更新数据
    window.rightData[moduleName] = newModuleData;

    // 记录操作历史
    storage.addHistory({
        type: 'merge_records',
        module: moduleName,
        count: selectedIndexes.length,
        detail: `合并了 ${selectedIndexes.length} 条记录`
    });

    // 重新渲染
    compareData();
    closeModal('modal');
    showToast(`成功合并 ${selectedIndexes.length} 条记录`, 'success');
}

// 切换记录视图
function toggleRecordView(moduleName, viewType) {
    const container = document.getElementById(`recordView_${moduleName}`);
    if (!container) return;

    const moduleData1 = window.leftData[moduleName] || [];
    const moduleData2 = window.rightData[moduleName] || [];

    if (viewType === 'table') {
        const indexFields = getIndexFieldsForModule(moduleName);
        const { matched, unmatched1, unmatched2 } = matchRecords(
            Array.isArray(moduleData1) ? moduleData1 : [],
            Array.isArray(moduleData2) ? moduleData2 : [],
            indexFields
        );

        container.innerHTML = renderTableView(moduleName, moduleData1, moduleData2, matched, unmatched1, unmatched2);
    } else {
        // 详细视图（原有的展示方式）
        container.innerHTML = renderDetailView(moduleName, moduleData1, moduleData2);
    }
}

// 渲染详细视图
function renderDetailView(moduleName, data1, data2) {
    // 这里可以调用原有的渲染逻辑
    return '<div class="detail-view">详细视图实现中...</div>';
}

// 查看记录差异
function viewRecordDiff(moduleName, index1, index2) {
    const record1 = window.leftData[moduleName][index1];
    const record2 = window.rightData[moduleName][index2];

    const content = `
        <div class="record-diff-dialog">
            <h3>记录差异对比</h3>
            <table class="diff-table">
                <thead>
                    <tr>
                        <th>字段</th>
                        <th>版本1</th>
                        <th>版本2</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    ${Object.keys({...record1, ...record2}).map(key => {
        if (key === '报告分类' || key === '报告模块') return '';

        const value1 = record1[key];
        const value2 = record2[key];
        const hasChange = JSON.stringify(value1) !== JSON.stringify(value2);

        return `
                            <tr class="${hasChange ? 'changed' : ''}">
                                <td class="field-name">${key}</td>
                                <td class="value1">${formatValue(value1)}</td>
                                <td class="value2">${formatValue(value2)}</td>
                                <td class="status">
                                    ${hasChange ? '<span class="status-changed">已修改</span>' : '<span class="status-unchanged">无变化</span>'}
                                </td>
                            </tr>
                        `;
    }).join('')}
                </tbody>
            </table>
            <div class="dialog-actions">
                <button class="btn btn-primary" onclick="closeModal('modal')">关闭</button>
            </div>
        </div>
    `;

    showModal('记录差异', content);
}

// 接受新记录
function acceptNewRecord(moduleName, index) {
    const path = `${moduleName}[${index}]`;

    // 记录更改状态
    storage.setChangeStatus(path, 'accepted');

    // 记录操作历史
    storage.addHistory({
        type: 'accept_new_record',
        module: moduleName,
        index: index,
        detail: `接受新增记录: ${moduleName}[${index}]`
    });

    // 更新UI
    const row = document.querySelector(`.record-row.added[data-index="${index}"]`);
    if (row) {
        row.style.opacity = '0.5';
        const actionCell = row.querySelector('.record-actions');
        if (actionCell) {
            actionCell.innerHTML = '<span class="status-mark accepted-mark">✓ 已接受</span>';
        }
    }

    showToast('已接受新增记录', 'success');
}

// 拒绝记录删除
function rejectRecordRemoval(moduleName, index) {
    const path = `${moduleName}[${index}]`;

    // 记录更改状态
    storage.setChangeStatus(path, 'rejected');

    // 记录操作历史
    storage.addHistory({
        type: 'reject_removal',
        module: moduleName,
        index: index,
        detail: `保留记录: ${moduleName}[${index}]`
    });

    // 更新UI
    const row = document.querySelector(`.record-row.removed[data-index="${index}"]`);
    if (row) {
        row.style.opacity = '1';
        const actionCell = row.querySelector('.record-actions');
        if (actionCell) {
            actionCell.innerHTML = '<span class="status-mark rejected-mark">✗ 已保留</span>';
        }
    }

    showToast('已保留记录', 'warning');
}

// 全局导出函数
window.renderMultiRecordModule = renderMultiRecordModule;
window.showMergeDialog = showMergeDialog;
window.confirmMerge = confirmMerge;
window.toggleRecordView = toggleRecordView;
window.viewRecordDiff = viewRecordDiff;
window.acceptNewRecord = acceptNewRecord;
window.rejectRecordRemoval = rejectRecordRemoval;
window.matchRecords = matchRecords;