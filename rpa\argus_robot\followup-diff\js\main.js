// main.js - 主逻辑和初始化

// 初始化
function init() {
    renderModuleConfigs();
    setupKeyboardShortcuts();
    storage.set('dataVersion', 0);
}

// 切换模式
function switchMode(mode) {
    storage.set('currentMode', mode);

    // 记录操作历史
    storage.addHistory({
        type: 'mode_switch',
        mode: mode,
        detail: `切换到${mode === 'revision' ? '修订' : '对比'}模式`
    });

    // 更新标签样式
    document.querySelectorAll('.version-tab').forEach(tab => {
        tab.classList.toggle('active', tab.dataset.version === mode);
    });

    // 更新模式指示器
    const modeIndicator = document.getElementById('modeIndicator');
    const modeText = document.getElementById('modeText');

    if (mode === 'revision') {
        modeIndicator.classList.remove('diff-mode');
        modeText.textContent = '修订模式';

        // 显示修订相关UI
        document.getElementById('revisionSummary').style.display = 'flex';
        document.getElementById('diffStatusBar').style.display = 'none';
        document.getElementById('diffFilterGroup').style.display = 'none';
        document.getElementById('revisionFilterGroup').style.display = 'flex';
        document.getElementById('exportBtn').style.display = 'inline-flex';

        // 更新内容标题
        document.getElementById('contentTitle').textContent = '修订建议';
        document.getElementById('contentDescription').textContent = '基于版本1→版本2的更改，对版本3的修订建议';

        // 更新按钮文字
        document.getElementById('acceptAllBtn').innerHTML = '<span>✓</span>接受所有修订';
        document.getElementById('rejectAllBtn').innerHTML = '<span>✗</span>拒绝所有修订';
    } else {
        modeIndicator.classList.add('diff-mode');
        modeText.textContent = '对比模式';

        // 显示对比相关UI
        document.getElementById('revisionSummary').style.display = 'none';
        document.getElementById('diffStatusBar').style.display = 'flex';
        document.getElementById('diffFilterGroup').style.display = 'flex';
        document.getElementById('revisionFilterGroup').style.display = 'none';
        document.getElementById('exportBtn').style.display = 'none';

        // 更新内容标题
        document.getElementById('contentTitle').textContent = '对比结果';
        document.getElementById('contentDescription').textContent = '版本1 → 版本2';

        // 更新按钮文字
        document.getElementById('acceptAllBtn').innerHTML = '<span>✓</span>接受所有更改';
        document.getElementById('rejectAllBtn').innerHTML = '<span>✗</span>拒绝所有更改';
    }

    // 重新渲染内容
    if (window.leftData && window.rightData) {
        if (mode === 'revision' && window.targetData) {
            showRevisionMode();
        } else {
            compareData();
        }
    }
}

// 设置键盘快捷键
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        if (e.ctrlKey || e.metaKey) {
            if (e.key === 'a') {
                e.preventDefault();
                if (storage.get('currentMode') === 'revision') {
                    acceptAllRevisions();
                } else {
                    acceptAllChanges();
                }
            } else if (e.key === 'r') {
                e.preventDefault();
                if (storage.get('currentMode') === 'revision') {
                    rejectAllRevisions();
                } else {
                    rejectAllChanges();
                }
            }
        } else if (e.key === 'Tab') {
            e.preventDefault();
            const currentMode = storage.get('currentMode');
            switchMode(currentMode === 'diff' ? 'revision' : 'diff');
        }
    });
}

// 显示JSON输入模态框
function showJsonInputModal() {
    document.getElementById('jsonInputModal').style.display = 'block';
    // 如果已有数据，预填充
    if (window.leftData) {
        document.getElementById('jsonInput1').value = JSON.stringify(window.leftData, null, 2);
    }
    if (window.rightData) {
        document.getElementById('jsonInput2').value = JSON.stringify(window.rightData, null, 2);
    }
    if (window.targetData) {
        document.getElementById('jsonInput3').value = JSON.stringify(window.targetData, null, 2);
    }
}

// 解析JSON输入
function parseJsonInput() {
    const json1 = document.getElementById('jsonInput1').value.trim();
    const json2 = document.getElementById('jsonInput2').value.trim();
    const json3 = document.getElementById('jsonInput3').value.trim();

    if (!json1 || !json2) {
        showToast('请至少输入版本1和版本2的JSON数据', 'warning');
        return;
    }

    try {
        window.leftData = JSON.parse(json1);
        window.rightData = JSON.parse(json2);

        // 数据更改时，清空修订状态
        storage.clearRevisions();
        storage.clearChangeStatus();
        storage.set('dataVersion', storage.get('dataVersion') + 1);

        // 保存版本
        storage.addVersion({
            left: window.leftData,
            right: window.rightData,
            target: window.targetData
        });

        // 记录操作历史
        storage.addHistory({
            type: 'data_input',
            detail: '输入JSON数据'
        });

        if (json3) {
            window.targetData = JSON.parse(json3);
            // 更新版本标签显示
            document.querySelector('[data-version="revision"]').classList.add('has-data');
            console.log('版本3数据已加载:', Object.keys(window.targetData));
        } else {
            window.targetData = null;
            document.querySelector('[data-version="revision"]').classList.remove('has-data');
        }

        closeModal('jsonInputModal');

        // 如果有版本3且当前是修订模式，显示修订
        if (window.targetData && storage.get('currentMode') === 'revision') {
            showRevisionMode();
        } else {
            compareData();
        }

        showToast('JSON数据解析成功', 'success');

        // 自动切换到修订模式（如果有版本3）
        if (window.targetData && storage.get('currentMode') !== 'revision') {
            showToast('检测到版本3数据，已自动切换到修订模式', 'info');
            switchMode('revision');
        }
    } catch (error) {
        showToast('JSON解析失败: ' + error.message, 'error');
    }
}

// 加载示例数据
async function loadSampleData() {
    try {
        showToast('正在加载示例数据...', 'info');

        // 从data文件夹加载三个文件
        const [response1, response2, response3] = await Promise.all([
            fetch('./data/report-000.json'),
            fetch('./data/report-001.json'),
            fetch('./data/report-002.json')
        ]);

        if (!response1.ok || !response2.ok || !response3.ok) {
            // 如果文件不存在，使用测试数据
            console.log('使用测试数据');
            window.leftData = {
                "报告基础信息": {
                    "报告分类": "严重不良事件（SAE）",
                    "报告模块": "报告基础信息",
                    "报告编号": "SAE-001",
                    "首次获知日期": "2024-01-15"
                },
                "受试者信息": {
                    "报告模块": "受试者信息",
                    "受试者筛选号": "S001",
                    "受试者随机号": "R001",
                    "出生日期": "1980-05-20",
                    "性别": "男"
                },
                "试验用药信息": {
                    "报告模块": "试验用药信息",
                    "用药名称": "试验药物A",
                    "剂量": "100mg",
                    "给药途径": "口服"
                }
            };

            window.rightData = {
                "报告基础信息": {
                    "报告分类": "严重不良事件（SAE）",
                    "报告模块": "报告基础信息",
                    "报告编号": "SAE-001-V2",  // 修改
                    "首次获知日期": "2024-01-15",
                    "报告状态": "已审核"  // 新增
                },
                "受试者信息": {
                    "报告模块": "受试者信息",
                    "受试者筛选号": "S001",
                    "受试者随机号": "R001-UPDATED",  // 修改
                    "出生日期": "1980-05-20",
                    "性别": "男",
                    "体重": "70kg"  // 新增
                },
                "试验用药信息": {
                    "报告模块": "试验用药信息",
                    "用药名称": "试验药物A",
                    "剂量": "200mg",  // 修改
                    "给药途径": "静脉注射",  // 修改
                    "用药频次": "每日一次"  // 新增
                }
            };

            // 版本3保持部分版本1的值，以生成修订建议
            window.targetData = {
                "报告基础信息": {
                    "报告分类": "严重不良事件（SAE）",
                    "报告模块": "报告基础信息",
                    "报告编号": "SAE-001",  // 保持版本1的值
                    "首次获知日期": "2024-01-15"
                    // 缺少"报告状态"字段
                },
                "受试者信息": {
                    "报告模块": "受试者信息",
                    "受试者筛选号": "S001",
                    "受试者随机号": "R001",  // 保持版本1的值
                    "出生日期": "1980-05-20",
                    "性别": "男",
                    "体重": "70kg"  // 已经更新到版本2的值
                },
                "试验用药信息": {
                    "报告模块": "试验用药信息",
                    "用药名称": "试验药物A",
                    "剂量": "100mg",  // 保持版本1的值
                    "给药途径": "口服"  // 保持版本1的值
                    // 缺少"用药频次"字段
                }
            };
        } else {
            // 成功加载所有三个文件
            window.leftData = await response1.json();
            window.rightData = await response2.json();
            window.targetData = await response3.json();

            console.log('成功加载示例数据：');
            console.log('版本1 (report-000.json):', Object.keys(window.leftData).length, '个模块');
            console.log('版本2 (report-001.json):', Object.keys(window.rightData).length, '个模块');
            console.log('版本3 (report-002.json):', Object.keys(window.targetData).length, '个模块');
        }

        // 清空之前的更改状态
        storage.clearChangeStatus();
        storage.clearRevisions();

        storage.set('dataVersion', storage.get('dataVersion') + 1);

        // 保存版本
        storage.addVersion({
            left: window.leftData,
            right: window.rightData,
            target: window.targetData
        });

        // 记录操作历史
        storage.addHistory({
            type: 'data_load',
            detail: '加载示例数据 (report-000.json, report-001.json, report-002.json)'
        });

        // 更新版本标签显示
        document.querySelector('[data-version="revision"]').classList.add('has-data');

        compareData();
        showToast('示例数据加载成功。三个版本的数据已就绪！', 'success');

        // 如果已有版本3数据，提示用户可以使用修订模式
        if (window.targetData) {
            setTimeout(() => {
                showToast('提示：您可以先在对比模式下接受需要的更改，然后切换到修订模式查看修订建议。', 'info');
            }, 2000);
        }
    } catch (error) {
        console.error('加载示例数据失败:', error);
        showToast('加载数据失败: ' + error.message, 'error');
    }
}

// 文件上传
function uploadFiles() {
    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = true;
    input.accept = '.json';
    input.onchange = handleFileUpload;
    input.click();
}

// 处理文件上传
function handleFileUpload(event) {
    const files = Array.from(event.target.files);
    if (files.length < 2 || files.length > 3) {
        showToast('请选择2个或3个JSON文件进行对比', 'warning');
        return;
    }

    const readers = files.map(file => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result);
                    resolve(data);
                } catch (error) {
                    reject(error);
                }
            };
            reader.onerror = reject;
            reader.readAsText(file);
        });
    });

    Promise.all(readers)
        .then((results) => {
            window.leftData = results[0];
            window.rightData = results[1];
            window.targetData = results[2] || null;

            // 数据更改时，清空修订状态
            storage.clearRevisions();
            storage.clearChangeStatus();
            storage.set('dataVersion', storage.get('dataVersion') + 1);

            // 保存版本
            storage.addVersion({
                left: window.leftData,
                right: window.rightData,
                target: window.targetData
            });

            // 记录操作历史
            storage.addHistory({
                type: 'file_upload',
                detail: `上传${files.length}个文件`
            });

            if (window.targetData) {
                document.querySelector('[data-version="revision"]').classList.add('has-data');
            }

            compareData();
            showToast('文件上传成功', 'success');
        })
        .catch(error => {
            showToast('文件解析失败: ' + error.message, 'error');
        });
}

// 点击模态框外部关闭
window.onclick = function(event) {
    if (event.target.classList.contains('modal')) {
        event.target.style.display = 'none';
    }
}

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', init);