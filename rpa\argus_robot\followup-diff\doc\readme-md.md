# JSON数据对比与合并工具

这是一个功能强大的JSON数据对比和修订工具，支持三个版本的数据对比和智能修订建议。

## 主要功能

1. **对比模式**：对比版本1和版本2的差异
2. **修订模式**：基于已接受的更改，为版本3生成修订建议
3. **智能识别**：自动识别新增、删除、修改的字段
4. **状态管理**：支持接受/拒绝更改，并可撤销操作
5. **操作历史**：记录所有操作，支持版本管理
6. **导出功能**：导出应用修订后的JSON数据

## 文件结构

```
project/
├── index.html
├── css/
│   ├── style.css
├── js/
│   ├── storage.js
│   ├── utils.js
│   ├── ui.js
│   ├── diff.js
│   ├── revision.js
│   └── main.js
├── data/
│   ├── report-000.json
│   ├── report-001.json
│   └── report-002.json
└── README.md
```

## 功能改进

### 1. 实时统计更新
- 修复了点击接受/拒绝修订后统计数字不更新的问题
- 现在所有操作都会实时反映在统计面板上

### 2. "已是最新"状态显示
- 当版本3的值已经与版本2相同时，会显示明显的"已是最新"标记
- 这些项不需要操作，但会显示在列表中供用户了解

### 3. 撤销功能
- 所有接受/拒绝的操作都可以撤销
- 提供了撤销按钮，让用户可以更改之前的决定

### 4. 完整的操作历史
- 记录所有操作历史，包括：
  - 模式切换
  - 数据加载
  - 接受/拒绝更改
  - 导出操作
- 支持查看和管理历史记录

## 使用流程

### 基本使用步骤

1. **加载数据**
   - 点击"输入JSON数据"手动输入三个版本的JSON
   - 或点击"加载示例数据"使用预设数据
   - 或点击"上传文件"选择JSON文件

2. **对比模式**
   - 查看版本1和版本2的差异
   - 接受需要应用到版本3的更改
   - 拒绝不需要的更改

3. **修订模式**
   - 切换到修订模式查看建议
   - 系统会基于你接受的更改生成修订建议
   - 逐个或批量接受/拒绝修订

4. **导出结果**
   - 在修订模式下点击"导出结果"
   - 下载应用修订后的JSON文件

### 快捷键

- `Ctrl+A` / `Cmd+A`：接受所有更改/修订
- `Ctrl+R` / `Cmd+R`：拒绝所有更改/修订
- `Tab`：切换模式

## 模块索引配置

可以为每个模块配置索引字段，用于智能匹配数组中的项目：

1. 点击"模块索引配置"展开配置面板
2. 为每个模块添加索引字段（如"受试者筛选号"、"不良事件名称"等）
3. 系统会根据这些字段匹配数组项，而不是简单地按位置对比

## 过滤器功能

### 对比模式过滤器
- **全部**：显示所有字段
- **新增**：只显示新增的字段
- **删除**：只显示删除的字段
- **修改**：只显示修改的字段

### 修订模式过滤器
- **全部**：显示所有修订建议
- **待处理**：只显示未处理的修订
- **已接受**：只显示已接受的修订
- **已拒绝**：只显示已拒绝的修订
- **已是最新**：只显示已经是最新的项目

## 注意事项

1. 修订模式需要所有三个版本的数据
2. 只有在对比模式下接受的更改才会生成修订建议
3. "已是最新"的项目不需要操作，仅供参考
4. 所有操作都可以通过操作历史查看和追踪

## 技术特点

- 纯前端实现，无需服务器
- 支持大型JSON文件处理
- 智能的数组项匹配算法
- 完整的状态管理系统
- 响应式设计，支持移动设备