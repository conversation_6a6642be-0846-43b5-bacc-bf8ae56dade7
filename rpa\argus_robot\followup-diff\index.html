<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON数据对比与合并工具</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/multi-record-styles.css">
</head>
<body>
<div class="app-container">
    <!-- 应用头部 -->
    <div class="app-header">
        <div class="header-content">
            <div style="display: flex; align-items: center;">
                <h1 class="app-title">
                    <span>🔄</span>
                    JSON数据对比与合并工具
                </h1>
                <span class="mode-indicator" id="modeIndicator">
                    <span>📝</span>
                    <span id="modeText">对比模式</span>
                </span>
            </div>
            <div class="version-control">
                <button class="btn btn-secondary" onclick="showHistory()">
                    <span>📜</span>
                    操作历史
                </button>
                <button class="btn btn-secondary" onclick="showVersionHistory()">
                    <span>📋</span>
                    版本历史
                </button>
                <button class="btn btn-secondary" onclick="debugData()">
                    <span>🐛</span>
                    调试数据
                </button>
                <button class="btn btn-info" id="exportBtn" onclick="exportResult()" style="display: none;">
                    <span>💾</span>
                    导出结果
                </button> 
                <button class="btn btn-success" id="acceptAllBtn" onclick="acceptAllChanges()">
                    <span>✓</span>
                    接受所有更改
                </button>
                <button class="btn btn-danger" id="rejectAllBtn" onclick="rejectAllChanges()">
                    <span>✗</span>
                    拒绝所有更改
                </button>
            </div>
        </div>
    </div>

    <!-- 版本选择器 -->
    <div class="version-selector">
        <div class="version-tabs">
            <button class="version-tab active" data-version="diff" onclick="switchMode('diff')">
                对比模式 (版本1 ↔ 版本2)
            </button>
            <button class="version-tab" data-version="revision" onclick="switchMode('revision')">
                修订模式 (版本3)
            </button>
        </div>
        <div class="data-source-buttons">
            <button class="btn btn-primary" onclick="showJsonInputModal()">
                <span>📝</span>
                输入JSON数据
            </button>
            <button class="btn btn-secondary" onclick="loadSampleData()">
                <span>📁</span>
                加载数据
            </button>
            <button class="btn btn-secondary" onclick="uploadFiles()">
                <span>⬆</span>
                上传文件
            </button>
        </div>
    </div>

    <!-- 状态栏 -->
    <div class="status-bar" id="diffStatusBar">
        <div class="status-info">
            <div class="status-item">
                <div class="status-indicator status-added"></div>
                <span>新增: <span id="addedCount">0</span></span>
            </div>
            <div class="status-item">
                <div class="status-indicator status-removed"></div>
                <span>删除: <span id="removedCount">0</span></span>
            </div>
            <div class="status-item">
                <div class="status-indicator status-modified"></div>
                <span>修改: <span id="modifiedCount">0</span></span>
            </div>
        </div>
    </div>

    <!-- 修订摘要（仅在修订模式显示） -->
    <div class="revision-summary" id="revisionSummary" style="display: none;">
        <div class="revision-summary-info">
            <div class="revision-summary-item">
                <div class="status-indicator status-revision"></div>
                <span>待处理修订: <span id="pendingRevisions">0</span></span>
            </div>
            <div class="revision-summary-item">
                <div class="status-indicator status-added"></div>
                <span>已接受: <span id="acceptedRevisions">0</span></span>
            </div>
            <div class="revision-summary-item">
                <div class="status-indicator status-removed"></div>
                <span>已拒绝: <span id="rejectedRevisions">0</span></span>
            </div>
            <div class="revision-summary-item">
                <div class="status-indicator status-info"></div>
                <span>已是最新: <span id="upToDateRevisions">0</span></span>
            </div>
        </div>
        <div class="revision-summary-actions">
            <button class="btn btn-success" onclick="acceptAllRevisions()">
                <span>✓</span>
                接受所有修订
            </button>
            <button class="btn btn-danger" onclick="rejectAllRevisions()">
                <span>✗</span>
                拒绝所有修订
            </button>
            <button class="btn btn-primary" id="convertAndSubmitBtn" onclick="convertAndSubmit()" style="display: none;">
                <span>🔄</span>
                转换并录入
            </button>
        </div>
    </div>

    <!-- 过滤器 -->
    <div class="filter-section">
        <div class="filter-group" id="diffFilterGroup">
            <span class="filter-label">显示更改：</span>
            <div class="filter-buttons">
                <button class="filter-btn active" data-filter="all" onclick="setFilter('all')">
                    全部
                </button>
                <button class="filter-btn" data-filter="added" onclick="setFilter('added')">
                    新增
                </button>
                <button class="filter-btn" data-filter="removed" onclick="setFilter('removed')">
                    删除
                </button>
                <button class="filter-btn" data-filter="modified" onclick="setFilter('modified')">
                    修改
                </button>
            </div>
        </div>
        <div class="filter-group" id="revisionFilterGroup" style="display: none;">
            <span class="filter-label">显示修订：</span>
            <div class="filter-buttons">
                <button class="revision-filter-btn active" data-filter="all" onclick="setRevisionFilter('all')">
                    全部
                </button>
                <button class="revision-filter-btn" data-filter="pending" onclick="setRevisionFilter('pending')">
                    待处理
                </button>
                <button class="revision-filter-btn" data-filter="accepted" onclick="setRevisionFilter('accepted')">
                    已接受
                </button>
                <button class="revision-filter-btn" data-filter="rejected" onclick="setRevisionFilter('rejected')">
                    已拒绝
                </button>
                <button class="revision-filter-btn" data-filter="uptodate" onclick="setRevisionFilter('uptodate')">
                    已是最新
                </button>
            </div>
        </div>
        <div class="filter-group">
            <button class="btn btn-secondary" onclick="expandAllModules()">展开全部</button>
            <button class="btn btn-secondary" onclick="collapseAllModules()">折叠全部</button>
        </div>
    </div>

    <!-- 配置面板 -->
    <div class="config-panel collapsed">
        <div class="config-panel-header" onclick="toggleConfigPanel()">
            <div class="config-panel-title">
                <span class="config-panel-arrow">▼</span>
                模块索引配置
            </div>
        </div>
        <div class="config-panel-body">
            <div id="moduleConfigs"></div>
        </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <div class="content-header">
            <h2 class="content-title" id="contentTitle">对比结果</h2>
            <div>
                <span style="color: #6b7280; font-size: 14px;" id="contentDescription">版本1 → 版本2</span>
            </div>
        </div>
        <div class="content-body" id="mainContent">
            <div class="empty-state">
                <div class="empty-state-icon">📄</div>
                <div class="empty-state-text">请加载或输入JSON数据进行对比</div>
            </div>
        </div>
    </div>

    <!-- 快捷键提示 -->
    <div class="shortcuts">
        快捷键: <span class="shortcut-key">Ctrl+A</span> 接受全部 | <span class="shortcut-key">Ctrl+R</span> 拒绝全部 | <span class="shortcut-key">Tab</span> 切换模式
    </div>
</div>

<!-- JSON输入模态框 -->
<div class="modal" id="jsonInputModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title">输入JSON数据</h2>
            <span class="modal-close" onclick="closeModal('jsonInputModal')">&times;</span>
        </div>
        <div class="json-input-container">
            <div class="json-input-section">
                <label class="json-input-label">
                    版本 1 JSON数据
                    <span class="version-badge">基准版本</span>
                </label>
                <textarea class="json-textarea" id="jsonInput1" placeholder="请粘贴第一个版本的JSON数据..."></textarea>
            </div>
            <div class="json-input-section">
                <label class="json-input-label">
                    版本 2 JSON数据
                    <span class="version-badge">对比版本</span>
                </label>
                <textarea class="json-textarea" id="jsonInput2" placeholder="请粘贴第二个版本的JSON数据..."></textarea>
            </div>
            <div class="json-input-section">
                <label class="json-input-label">
                    版本 3 JSON数据
                    <span class="version-badge primary">目标版本（可选）</span>
                </label>
                <textarea class="json-textarea" id="jsonInput3" placeholder="请粘贴第三个版本的JSON数据（用于修订）..."></textarea>
            </div>
        </div>
        <div class="json-input-actions">
            <button class="btn btn-secondary" onclick="closeModal('jsonInputModal')">取消</button>
            <button class="btn btn-primary" onclick="parseJsonInput()">
                <span>✓</span>
                确认对比
            </button>
        </div>
    </div>
</div>

<!-- 通用模态框 -->
<div class="modal" id="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title" id="modalTitle">标题</h2>
            <span class="modal-close" onclick="closeModal('modal')">&times;</span>
        </div>
        <div id="modalBody"></div>
    </div>
</div>

<!-- Toast提示 -->
<div class="toast" id="toast">
    <span class="toast-icon"></span>
    <span class="toast-message"></span>
</div>

<!-- 引入JavaScript模块 -->
<script src="js/storage.js"></script>
<script src="js/utils.js"></script>
<script src="js/ui.js"></script>
<script src="js/diff.js"></script>
<script src="js/revision.js"></script>
<script src="js/data-loader-js.js"></script>
<script src="js/multi-record-ui.js"></script>
<script src="js/main.js"></script>
</body>
</html>