// 工具函数

// 清理对象，移除undefined的键和值
function cleanObject(obj) {
    if (!obj || typeof obj !== 'object') return obj;

    const cleaned = {};
    for (const [key, value] of Object.entries(obj)) {
        if (key !== undefined && key !== 'undefined' && value !== undefined) {
            if (typeof value === 'object' && !Array.isArray(value)) {
                cleaned[key] = cleanObject(value);
            } else {
                cleaned[key] = value;
            }
        }
    }
    return cleaned;
}

// 格式化值
function formatValue(value) {
    if (value === null) return 'null';
    if (value === undefined || value === 'undefined') return '';
    if (typeof value === 'string') return value || '(空)';
    if (typeof value === 'object') return JSON.stringify(value);
    return String(value);
}

// 按模块组织数据
function organizeByModules(data) {
    const modules = {};

    // 遍历所有字段
    for (const [key, value] of Object.entries(data)) {
        if (key === 'datetime') continue; // 跳过特殊字段

        // 检查是否是数组（多记录模块）
        if (Array.isArray(value)) {
            // 数组类型，可能是多记录模块
            if (value.length > 0 && value[0]['报告模块']) {
                // 从第一条记录获取模块名
                const moduleName = value[0]['报告模块'];
                modules[moduleName] = value;
            } else {
                // 没有报告模块字段，使用key作为模块名
                modules[key] = value;
            }
        } else if (typeof value === 'object' && value !== null) {
            // 对象类型
            if (value['报告模块']) {
                const moduleName = value['报告模块'];
                if (!modules[moduleName]) {
                    modules[moduleName] = {};
                }
                // 如果key和moduleName相同，直接将value的内容展开到模块中
                if (key === moduleName) {
                    Object.assign(modules[moduleName], value);
                } else {
                    modules[moduleName][key] = value;
                }
            } else {
                // 可能是一个顶级模块对象，使用key作为模块名
                if (!modules[key]) {
                    modules[key] = {};
                }
                // 直接将value的内容展开，避免双重嵌套
                Object.assign(modules[key], value);
            }
        } else {
            // 其他类型的数据，归类到"其他"模块
            if (!modules['其他']) {
                modules['其他'] = {};
            }
            modules['其他'][key] = value;
        }
    }

    console.log('组织后的模块数据:', Object.keys(modules));
    return modules;
}
// 获取模块的索引字段
function getIndexFieldsForModule(moduleName) {
    const config = storage.getModuleIndexConfig();

    // 返回模块特定的索引字段
    let moduleFields = config[moduleName];
    if (Array.isArray(moduleFields)) {
        return moduleFields;
    }

    // 如果没有配置，返回空数组
    return [];
}

// 分析变化
function analyzeChange(value1, value2) {
    if (value1 === undefined && value2 !== undefined) {
        return { hasChange: true, type: 'added' };
    } else if (value1 !== undefined && value2 === undefined) {
        return { hasChange: true, type: 'removed' };
    } else if (JSON.stringify(value1) !== JSON.stringify(value2)) {
        return { hasChange: true, type: 'modified' };
    }
    return { hasChange: false };
}

// 判断是否应该应用修订
function shouldApplyRevision(value1, value2, value3) {
    // 如果版本2相对版本1有变化
    if (JSON.stringify(value1) !== JSON.stringify(value2)) {
        // 情况1：版本3的值等于版本1的值（未应用更改）
        if (JSON.stringify(value3) === JSON.stringify(value1)) {
            return true;
        }

        // 情况2：版本3是undefined，但版本2有值（新增的字段）
        if (value3 === undefined && value2 !== undefined) {
            return true;
        }

        // 情况3：版本3与版本2不同（需要修订）
        // 只要版本3的值不等于版本2的值，就应该生成修订建议
        if (JSON.stringify(value3) !== JSON.stringify(value2)) {
            return true;
        }
    }

    return false;
}

// 获取操作类型的中文名称
function getOperationTypeName(type) {
    const typeNames = {
        'mode_switch': '切换模式',
        'data_input': '输入数据',
        'data_load': '加载数据',
        'file_upload': '上传文件',
        'accept_revision': '接受修订',
        'reject_revision': '拒绝修订',
        'undo_revision': '撤销修订',
        'accept_all': '接受全部',
        'reject_all': '拒绝全部',
        'accept_module': '接受模块',
        'reject_module': '拒绝模块',
        'export': '导出数据',
        'load_version': '加载版本',
        'accept_change': '接受更改',
        'reject_change': '拒绝更改'
    };
    return typeNames[type] || type;
}

// 获取操作类型的图标
function getOperationIcon(type) {
    const icons = {
        'mode_switch': '🔄',
        'data_input': '📝',
        'data_load': '📁',
        'file_upload': '⬆️',
        'accept_revision': '✅',
        'reject_revision': '❌',
        'undo_revision': '↺',
        'accept_all': '✅',
        'reject_all': '❌',
        'accept_module': '✅',
        'reject_module': '❌',
        'export': '💾',
        'load_version': '📋',
        'accept_change': '✅',
        'reject_change': '❌'
    };
    return icons[type] || '📄';
}

// 格式化时间
function formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;

    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;

    return date.toLocaleString('zh-CN');
}

// 深拷贝对象
function deepClone(obj) {
    return JSON.parse(JSON.stringify(obj));
}

// 使deepClone全局可用
window.deepClone = deepClone;