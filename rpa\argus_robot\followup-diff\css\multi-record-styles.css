/* 状态标记 */
.status-mark {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
}

.status-mark.accepted-mark {
    background: #d1fae5;
    color: #065f46;
}

.status-mark.rejected-mark {
    background: #fee2e2;
    color: #991b1b;
}/* 多记录模块样式 */
.multi-record-module .module-content {
    padding: 0;
}

/* 工具栏 */
.multi-record-toolbar {
    padding: 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    gap: 12px;
    align-items: center;
}

.multi-record-toolbar .btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

/* 表格容器 */
.multi-record-table-container {
    overflow-x: auto;
    padding: 16px;
}

/* 多记录表格 */
.multi-record-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
}

.multi-record-table th {
    background: #f3f4f6;
    padding: 12px;
    text-align: left;
    font-weight: 600;
    font-size: 13px;
    color: #374151;
    border-bottom: 2px solid #e5e7eb;
    position: sticky;
    top: 0;
    z-index: 10;
}

.multi-record-table td {
    padding: 12px;
    border-bottom: 1px solid #f3f4f6;
    font-size: 14px;
}

/* 索引字段样式 */
.multi-record-table th.index-field {
    background: #e0f2fe;
    color: #0369a1;
}

.index-indicator {
    font-size: 12px;
    margin-left: 4px;
}

/* 版本列 */
.record-version {
    width: 80px;
    text-align: center;
}

.version-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    background: #e5e7eb;
    color: #374151;
}

.version-badge.version2 {
    background: #dbeafe;
    color: #1e40af;
}

/* 状态列 */
.record-status {
    width: 100px;
    text-align: center;
}

.status-modified {
    color: #f59e0b;
    font-weight: 600;
}

.status-unchanged {
    color: #10b981;
}

.status-added {
    color: #10b981;
    font-weight: 600;
}

.status-removed {
    color: #ef4444;
    font-weight: 600;
}

/* 字段变化样式 */
.field-changed {
    background: #fef3c7;
}

.field-added {
    background: #d1fae5;
}

.field-removed {
    background: #fee2e2;
    text-decoration: line-through;
    opacity: 0.7;
}

.old-value {
    color: #ef4444;
    text-decoration: line-through;
}

.new-value {
    color: #10b981;
    font-weight: 600;
}

/* 记录行样式 */
.record-row.version1 {
    background: #fafafa;
}

.record-row.version2 {
    background: #f0f9ff;
}

.record-row.added {
    background: #f0fdf4;
}

.record-row.removed {
    background: #fef2f2;
}

.record-separator td {
    height: 4px;
    background: #e5e7eb;
    padding: 0;
}

/* 操作列 */
.record-actions {
    width: 150px;
    text-align: center;
}

.action-btn {
    padding: 4px 12px;
    font-size: 12px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: all 0.2s;
}

.action-btn.view-btn {
    background: #3b82f6;
    color: white;
}

.action-btn.view-btn:hover {
    background: #2563eb;
}

.action-btn.accept-btn {
    background: #10b981;
    color: white;
}

.action-btn.accept-btn:hover {
    background: #059669;
}

.action-btn.reject-btn {
    background: #ef4444;
    color: white;
}

.action-btn.reject-btn:hover {
    background: #dc2626;
}

/* 合并复选框 */
.merge-checkbox {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #6b7280;
    cursor: pointer;
    margin-top: 8px;
}

.merge-checkbox input {
    cursor: pointer;
}

/* 合并对话框 */
.merge-dialog {
    max-width: 600px;
}

.merge-preview {
    margin: 20px 0;
    padding: 16px;
    background: #f9fafb;
    border-radius: 8px;
}

.merge-preview h4 {
    margin: 0 0 12px 0;
    color: #374151;
}

.merge-preview-table {
    width: 100%;
    background: white;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.merge-preview-table tr {
    border-bottom: 1px solid #f3f4f6;
}

.merge-preview-table tr:last-child {
    border-bottom: none;
}

.merge-preview-table td {
    padding: 10px 12px;
}

.merge-preview-table .field-name {
    font-weight: 600;
    color: #374151;
    width: 40%;
    background: #f9fafb;
}

.merge-preview-table .field-value {
    color: #111827;
}

.merge-preview-table tr.has-conflict {
    background: #fef3c7;
}

.conflict-indicator {
    margin-left: 8px;
    color: #f59e0b;
    font-size: 12px;
}

.merge-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
}

/* 记录差异对话框 */
.record-diff-dialog {
    max-width: 800px;
}

.diff-table {
    width: 100%;
    margin-top: 16px;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.diff-table th {
    background: #f3f4f6;
    padding: 12px;
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid #e5e7eb;
}

.diff-table td {
    padding: 12px;
    border-bottom: 1px solid #f3f4f6;
}

.diff-table tr.changed {
    background: #fef3c7;
}

.diff-table .field-name {
    font-weight: 600;
    color: #374151;
}

.diff-table .value1 {
    color: #6b7280;
}

.diff-table .value2 {
    color: #111827;
}

.diff-table .status-changed {
    color: #f59e0b;
    font-weight: 600;
}

.diff-table .status-unchanged {
    color: #10b981;
}

.dialog-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .multi-record-table {
        font-size: 12px;
    }

    .multi-record-table th,
    .multi-record-table td {
        padding: 8px;
    }

    .record-actions {
        width: 100px;
    }

    .action-btn {
        padding: 2px 8px;
        font-size: 11px;
    }
}