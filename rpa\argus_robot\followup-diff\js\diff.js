// diff.js - 对比相关功能

// 全局变量
window.leftData = null;   // 版本1
window.rightData = null;  // 版本2
window.targetData = null; // 版本3
window.mergedData = null;
window.diffStats = { added: 0, removed: 0, modified: 0 };
window.changeMap = new Map(); // 存储所有更改

// 数据对比
function compareData() {
    if (!window.leftData || !window.rightData) return;

    window.diffStats = { added: 0, removed: 0, modified: 0 };
    window.changeMap.clear();

    // 清理数据
    const cleanedLeftData = cleanObject(window.leftData);
    const cleanedRightData = cleanObject(window.rightData);

    // 按模块组织数据
    const leftModules = organizeByModules(cleanedLeftData);
    const rightModules = organizeByModules(cleanedRightData);

    // 渲染合并的对比结果
    renderMergedComparison(leftModules, rightModules);

    updateStats();
}

// 渲染合并的对比结果
function renderMergedComparison(leftModules, rightModules) {
    const container = document.getElementById('mainContent');
    let html = '';

    // 获取所有模块名
    const allModules = new Set([...Object.keys(leftModules), ...Object.keys(rightModules)]);

    for (const moduleName of allModules) {
        const module1Data = leftModules[moduleName] || {};
        const module2Data = rightModules[moduleName] || {};

        const moduleStats = { added: 0, removed: 0, modified: 0 };

        // 直接使用模块数据进行对比
        const comparison = compareModuleData(module1Data, module2Data, moduleName, moduleStats);

        // 检查是否是多记录模块
        if (comparison && comparison.isMultiRecord) {
            // 使用多记录模块渲染
            html += renderMultiRecordModule(moduleName, comparison.data1, comparison.data2, moduleStats);
        } else if (comparison && Object.keys(comparison).length > 0) {
            // 使用原有的渲染方式
            html += renderMergedModule(moduleName, comparison, moduleStats);
        }
    }

    container.innerHTML = html || '<div class="empty-state"><div class="empty-state-icon">📄</div><div class="empty-state-text">无数据</div></div>';

    // 应用当前过滤器
    applyFilter();
}

// 对比模块数据
function compareModuleData(data1, data2, moduleName, stats) {
    const result = {};
    const allKeys = new Set([...Object.keys(data1), ...Object.keys(data2)]);

    for (const key of allKeys) {
        // 跳过undefined的键
        if (key === undefined || key === 'undefined') continue;

        const value1 = data1[key];
        const value2 = data2[key];

        if (value1 === undefined) {
            stats.added++;
            window.diffStats.added++;
            result[key] = { status: 'added', value: value2 };
        } else if (value2 === undefined) {
            stats.removed++;
            window.diffStats.removed++;
            result[key] = { status: 'removed', value: value1 };
        } else if (Array.isArray(value1) && Array.isArray(value2)) {
            // 数组对比
            result[key] = compareArrayData(value1, value2, moduleName, stats);
        } else if (typeof value1 === 'object' && typeof value2 === 'object') {
            // 对象对比
            result[key] = compareObjectData(value1, value2, stats);
        } else if (JSON.stringify(value1) !== JSON.stringify(value2)) {
            stats.modified++;
            window.diffStats.modified++;
            result[key] = { status: 'modified', value: value2, oldValue: value1 };
        } else {
            result[key] = { status: 'unchanged', value: value1 };
        }
    }

    return result;
}

// 对比数组数据
function compareArrayData(arr1, arr2, moduleName, stats) {
    const indexFields = getIndexFieldsForModule(moduleName);

    const matched = new Map();
    const result = [];

    // 根据索引字段匹配记录
    if (indexFields.length > 0) {
        // 建立索引
        const index2Map = new Map();
        arr2.forEach((item, idx) => {
            const keys = indexFields.map(field => item[field]).filter(v => v).join('|');
            if (keys) {
                index2Map.set(keys, idx);
            }
        });

        // 匹配记录
        arr1.forEach((item1, idx1) => {
            const keys = indexFields.map(field => item1[field]).filter(v => v).join('|');
            if (keys && index2Map.has(keys)) {
                const idx2 = index2Map.get(keys);
                matched.set(idx2, true);
                const comparison = compareObjectData(item1, arr2[idx2], stats);
                result.push({ ...comparison, index: idx1, matchedIndex: idx2 });
            } else {
                stats.removed++;
                window.diffStats.removed++;
                result.push({ status: 'removed', value: item1, index: idx1 });
            }
        });

        // 处理未匹配的记录
        arr2.forEach((item2, idx2) => {
            if (!matched.has(idx2)) {
                stats.added++;
                window.diffStats.added++;
                result.push({ status: 'added', value: item2, index: arr1.length + idx2 });
            }
        });
    } else {
        // 按索引位置对比
        const maxLen = Math.max(arr1.length, arr2.length);
        for (let i = 0; i < maxLen; i++) {
            if (i >= arr1.length) {
                stats.added++;
                window.diffStats.added++;
                result.push({ status: 'added', value: arr2[i], index: i });
            } else if (i >= arr2.length) {
                stats.removed++;
                window.diffStats.removed++;
                result.push({ status: 'removed', value: arr1[i], index: i });
            } else {
                const comparison = compareObjectData(arr1[i], arr2[i], stats);
                result.push({ ...comparison, index: i });
            }
        }
    }

    return { status: 'array', items: result };
}

// 对比对象数据
function compareObjectData(obj1, obj2, stats) {
    const fields = {};
    const allKeys = new Set([...Object.keys(obj1), ...Object.keys(obj2)]);

    let hasChanges = false;
    for (const key of allKeys) {
        if (key === '报告分类' || key === '报告模块' || key === undefined || key === 'undefined') continue; // 跳过元数据和undefined键

        const value1 = obj1[key];
        const value2 = obj2[key];

        if (value1 === undefined) {
            stats.added++;
            window.diffStats.added++;
            fields[key] = { status: 'added', value: value2 };
            hasChanges = true;
        } else if (value2 === undefined) {
            stats.removed++;
            window.diffStats.removed++;
            fields[key] = { status: 'removed', value: value1 };
            hasChanges = true;
        } else if (JSON.stringify(value1) !== JSON.stringify(value2)) {
            stats.modified++;
            window.diffStats.modified++;
            fields[key] = { status: 'modified', value: value2, oldValue: value1 };
            hasChanges = true;
        } else {
            fields[key] = { status: 'unchanged', value: value1 };
        }
    }

    return { status: hasChanges ? 'object' : 'unchanged', fields };
}

// 渲染合并的模块
function renderMergedModule(moduleName, comparison, stats) {
    const hasChanges = stats.added > 0 || stats.removed > 0 || stats.modified > 0;
    const hasData = comparison && Object.keys(comparison).length > 0;

    let html = `
        <div class="module-section ${hasChanges ? '' : 'collapsed'}" data-module="${moduleName}">
            <div class="module-header ${hasChanges ? 'has-changes' : ''}" onclick="toggleModule(this)">
                <div class="module-title">
                    <span class="module-arrow">▼</span>
                    ${moduleName}
                </div>
                <div class="module-stats">
                    ${stats.added > 0 ? `<span class="stat-badge stat-added">+${stats.added}</span>` : ''}
                    ${stats.removed > 0 ? `<span class="stat-badge stat-removed">-${stats.removed}</span>` : ''}
                    ${stats.modified > 0 ? `<span class="stat-badge stat-modified">~${stats.modified}</span>` : ''}
                    ${!hasChanges && hasData ? '<span class="stat-badge stat-unchanged">无变化</span>' : ''}
                </div>
            </div>
            <div class="module-content">
    `;

    if (!hasData) {
        html += `
            <div class="empty-module-message">
                <span class="empty-module-icon">📋</span>
                <span class="empty-module-text">此模块暂无数据</span>
            </div>
        `;
    } else if (!hasChanges) {
        html += `
            <div class="no-changes-message">
                <span class="no-changes-icon">✓</span>
                <span class="no-changes-text">此模块数据完全一致，无需合并</span>
            </div>
        `;
    } else {
        // 渲染有变化的内容
        for (const [key, data] of Object.entries(comparison)) {
            if (key !== undefined && key !== 'undefined' && data !== undefined) {
                html += renderMergedField(key, data, `${moduleName}.${key}`);
            }
        }
    }

    html += `
            </div>
        </div>
    `;

    return html;
}

// 渲染合并的字段
function renderMergedField(fieldName, data, path) {
    // 跳过undefined的字段
    if (fieldName === undefined || fieldName === 'undefined' || data === undefined) {
        return '';
    }

    let html = '';

    if (data.status === 'array') {
        // 渲染数组
        html = `
            <div class="field-row">
                <div class="field-label">${fieldName}</div>
                <div class="field-value-container">
                    <div class="array-container">
        `;

        data.items.forEach((item, index) => {
            html += renderMergedArrayItem(item, `${path}[${index}]`);
        });

        html += `
                    </div>
                </div>
            </div>
        `;
    } else if (data.status === 'object' && data.fields) {
        // 渲染对象
        html = `<div class="field-group">`;

        for (const [key, value] of Object.entries(data.fields)) {
            if (key !== undefined && key !== 'undefined' && value !== undefined) {
                html += renderMergedSimpleField(key, value, `${path}.${key}`);
            }
        }

        html += `</div>`;
    } else {
        // 渲染简单字段
        html = renderMergedSimpleField(fieldName, data, path);
    }

    return html;
}

// 渲染合并的数组项
function renderMergedArrayItem(item, path) {
    if (!item || item === undefined) return '';

    const statusClass = item.status !== 'unchanged' ? item.status : '';

    // 检查这个数组项是否已经被处理过
    const isAccepted = storage.isChangeAccepted(path);
    const isRejected = storage.isChangeRejected(path);

    let html = `<div class="array-item ${statusClass}" data-path="${path}" style="${isRejected ? 'opacity: 0.3;' : isAccepted ? 'opacity: 0.5;' : ''}">`;

    if (item.status === 'object' && item.fields) {
        // 查找标识字段
        let identifier = '';
        for (const [key, value] of Object.entries(item.fields)) {
            if ((key.includes('名称') || key.includes('日期')) && value && value.value) {
                identifier = value.value;
                break;
            }
        }

        if (identifier) {
            html += `<div class="array-item-header">${identifier}</div>`;
        }

        // 渲染字段
        for (const [key, value] of Object.entries(item.fields)) {
            if (key !== undefined && key !== 'undefined' && value !== undefined) {
                html += renderMergedSimpleField(key, value, `${path}.${key}`);
            }
        }
    } else {
        html += renderMergedSimpleField('值', item, path);
    }

    // 如果是数组级别的更改，添加操作按钮
    if (item.status !== 'unchanged' && !isAccepted && !isRejected) {
        html += `
            <div class="revision-actions" style="margin-top: 12px;">
                <button class="action-btn accept-btn" onclick="acceptChange('${path}')" title="接受更改">
                    ✓ 接受整个项目
                </button>
                <button class="action-btn reject-btn" onclick="rejectChange('${path}')" title="拒绝更改">
                    ✗ 拒绝整个项目
                </button>
            </div>
        `;

        // 记录更改
        window.changeMap.set(path, { type: item.status, value: item });
    }

    html += `</div>`;
    return html;
}

// 渲染合并的简单字段
function renderMergedSimpleField(fieldName, data, path) {
    // 跳过undefined的字段和无变化的字段（除非是全部显示模式）
    if (fieldName === undefined || fieldName === 'undefined' || data === undefined) {
        return '';
    }

    // 如果数据没有变化，根据过滤器决定是否显示
    if (data.status === 'unchanged' && storage.get('currentFilter') !== 'all') {
        return '';
    }

    let valueHtml = '';

    // 检查这个更改是否已经被处理过
    const isAccepted = storage.isChangeAccepted(path);
    const isRejected = storage.isChangeRejected(path);

    if (data.status === 'added') {
        valueHtml = `<span class="revision-added">${formatValue(data.value)}</span>`;
        window.changeMap.set(path, { type: 'added', value: data.value });
    } else if (data.status === 'removed') {
        valueHtml = `<span class="revision-removed">${formatValue(data.value)}</span>`;
        window.changeMap.set(path, { type: 'removed', value: data.value });
    } else if (data.status === 'modified') {
        valueHtml = `
            <div class="revision-modified">
                <span class="revision-old">${formatValue(data.oldValue)}</span>
                <span class="revision-arrow">→</span>
                <span class="revision-new">${formatValue(data.value)}</span>
            </div>
        `;
        window.changeMap.set(path, { type: 'modified', oldValue: data.oldValue, newValue: data.value });
    } else {
        valueHtml = formatValue(data.value);
    }

    const showActions = data.status !== 'unchanged' && !isAccepted && !isRejected;

    return `
        <div class="field-row" data-path="${path}" style="${isRejected ? 'opacity: 0.3;' : isAccepted ? 'opacity: 0.5;' : ''}">
            <div class="field-label">${fieldName}</div>
            <div class="field-value-container">
                ${valueHtml}
                ${isAccepted ? '<span class="status-mark accepted-mark" style="margin-left: 8px; color: var(--success); font-size: 12px; font-weight: 600;">✓ 已接受</span>' : ''}
                ${isRejected ? '<span class="status-mark rejected-mark" style="margin-left: 8px; color: var(--danger); font-size: 12px; font-weight: 600;">✗ 已拒绝</span>' : ''}
            </div>
            ${showActions ? `
                <div class="revision-actions">
                    <button class="action-btn accept-btn" onclick="acceptChange('${path}')" title="接受更改">
                        ✓
                    </button>
                    <button class="action-btn reject-btn" onclick="rejectChange('${path}')" title="拒绝更改">
                        ✗
                    </button>
                </div>
            ` : ''}
        </div>
    `;
}

// 接受更改
function acceptChange(path) {
    const change = window.changeMap.get(path);
    if (!change) return;

    // 记录更改状态
    storage.setChangeStatus(path, 'accepted');

    // 记录操作历史
    storage.addHistory({
        type: 'accept_change',
        mode: 'diff',
        path: path,
        detail: `接受更改: ${path}`
    });

    // 找到对应的DOM元素并更新显示
    const row = document.querySelector(`[data-path="${path}"]`);
    if (row) {
        row.style.opacity = '0.5';
        row.querySelector('.revision-actions')?.remove();

        // 添加已接受标记
        const valueContainer = row.querySelector('.field-value-container');
        if (valueContainer && !valueContainer.querySelector('.status-mark')) {
            const mark = document.createElement('span');
            mark.className = 'status-mark accepted-mark';
            mark.textContent = '✓ 已接受';
            mark.style.cssText = 'margin-left: 8px; color: var(--success); font-size: 12px; font-weight: 600;';
            valueContainer.appendChild(mark);
        }
    }

    showToast(`已接受更改: ${path}`, 'success');
}

// 拒绝更改
function rejectChange(path) {
    const change = window.changeMap.get(path);
    if (!change) return;

    // 记录更改状态
    storage.setChangeStatus(path, 'rejected');

    // 记录操作历史
    storage.addHistory({
        type: 'reject_change',
        mode: 'diff',
        path: path,
        detail: `拒绝更改: ${path}`
    });

    // 找到对应的DOM元素并更新显示
    const row = document.querySelector(`[data-path="${path}"]`);
    if (row) {
        row.style.opacity = '0.3';
        row.querySelector('.revision-actions')?.remove();

        // 添加已拒绝标记
        const valueContainer = row.querySelector('.field-value-container');
        if (valueContainer && !valueContainer.querySelector('.status-mark')) {
            const mark = document.createElement('span');
            mark.className = 'status-mark rejected-mark';
            mark.textContent = '✗ 已拒绝';
            mark.style.cssText = 'margin-left: 8px; color: var(--danger); font-size: 12px; font-weight: 600;';
            valueContainer.appendChild(mark);
        }
    }

    showToast(`已拒绝更改: ${path}`, 'warning');
}

// 接受所有更改
function acceptAllChanges() {
    const changes = Array.from(window.changeMap.keys());
    let count = 0;

    changes.forEach(path => {
        if (!storage.isChangeAccepted(path) && !storage.isChangeRejected(path)) {
            acceptChange(path);
            count++;
        }
    });

    // 记录操作历史
    if (count > 0) {
        storage.addHistory({
            type: 'accept_all',
            mode: 'diff',
            count: count,
            detail: `接受所有更改（${count}个）`
        });
    }

    showToast(`已接受 ${count} 个更改`, 'success');
}

// 拒绝所有更改
function rejectAllChanges() {
    const changes = Array.from(window.changeMap.keys());
    let count = 0;

    changes.forEach(path => {
        if (!storage.isChangeAccepted(path) && !storage.isChangeRejected(path)) {
            rejectChange(path);
            count++;
        }
    });

    // 记录操作历史
    if (count > 0) {
        storage.addHistory({
            type: 'reject_all',
            mode: 'diff',
            count: count,
            detail: `拒绝所有更改（${count}个）`
        });
    }

    showToast(`已拒绝 ${count} 个更改`, 'warning');
}

// 更新统计
function updateStats() {
    document.getElementById('addedCount').textContent = window.diffStats.added;
    document.getElementById('removedCount').textContent = window.diffStats.removed;
    document.getElementById('modifiedCount').textContent = window.diffStats.modified;
}